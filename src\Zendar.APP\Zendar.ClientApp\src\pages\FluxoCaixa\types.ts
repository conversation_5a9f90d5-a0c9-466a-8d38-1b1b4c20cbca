// Tipos para a estrutura de dados do fluxo de caixa

export interface FluxoCaixaValores {
  jan: number;
  fev: number;
  mar: number;
  abr: number;
  mai: number;
  jun: number;
  jul: number;
  ago: number;
  set: number;
  out: number;
  nov: number;
  dez: number;
  total: number;
}

export interface FluxoCaixaItem {
  id: string;
  nome: string;
  tipo: 'categoria' | 'subcategoria' | 'item';
  nivel: number;
  valores: FluxoCaixaValores;
  filhos?: FluxoCaixaItem[];
  expandido?: boolean;
  cor?: string;
  planoContaId?: string;
  codigo?: string;
}

export interface FluxoCaixaData {
  saldoInicial: FluxoCaixaItem;
  entradas: FluxoCaixaItem;
  saidas: FluxoCaixaItem;
  transferencias: FluxoCaixaItem;
  resultadoPeriodo: FluxoCaixaItem;
  saldoFinal: FluxoCaixaItem;
}

// Estrutura de resposta da API
export interface FluxoCaixaApiResponse {
  sucesso: boolean;
  dados: FluxoCaixaData;
  avisos?: string[];
  erros?: string[];
}

// Parâmetros para filtros da API
export interface FluxoCaixaFiltros {
  dataInicio: string; // formato: YYYY-MM-DD
  dataFim: string; // formato: YYYY-MM-DD
  contaFinanceiraId?: string;
  planoContaIds?: string[];
  incluirSubcontas?: boolean;
  agruparPorCategoria?: boolean;
}

// Estrutura para configuração de colunas
export interface FluxoCaixaColuna {
  mes: keyof FluxoCaixaValores;
  label: string;
  visivel: boolean;
}

// Configurações de exibição
export interface FluxoCaixaConfig {
  colunas: FluxoCaixaColuna[];
  moedaSimbol: string;
  formatoData: string;
  exibirTotalizadores: boolean;
  exibirPercentuais: boolean;
  nivelMaximoExpansao: number;
}

// Estados de carregamento e erro
export interface FluxoCaixaState {
  isLoading: boolean;
  error: string | null;
  data: FluxoCaixaData | null;
  filtros: FluxoCaixaFiltros;
  config: FluxoCaixaConfig;
  expandedItems: Set<string>;
}

// Ações para gerenciamento de estado
export type FluxoCaixaAction =
  | { type: 'SET_LOADING'; payload: boolean }
  | { type: 'SET_ERROR'; payload: string | null }
  | { type: 'SET_DATA'; payload: FluxoCaixaData }
  | { type: 'SET_FILTROS'; payload: Partial<FluxoCaixaFiltros> }
  | { type: 'TOGGLE_EXPANDED'; payload: string }
  | { type: 'SET_EXPANDED'; payload: Set<string> }
  | { type: 'UPDATE_CONFIG'; payload: Partial<FluxoCaixaConfig> };

// Utilitários para cálculos
export interface FluxoCaixaCalculos {
  totalEntradas: number;
  totalSaidas: number;
  saldoLiquido: number;
  variacao: number;
  percentualVariacao: number;
}

// Estrutura para exportação
export interface FluxoCaixaExportacao {
  formato: 'excel' | 'pdf' | 'csv';
  incluirGraficos: boolean;
  incluirDetalhes: boolean;
  periodo: {
    inicio: string;
    fim: string;
  };
}

// Metadados da resposta
export interface FluxoCaixaMetadata {
  dataUltimaAtualizacao: string;
  totalRegistros: number;
  tempoProcessamento: number;
  versaoApi: string;
}

export interface FluxoCaixaResponseCompleta {
  sucesso: boolean;
  dados: FluxoCaixaData;
  calculos: FluxoCaixaCalculos;
  metadata: FluxoCaixaMetadata;
  avisos?: string[];
  erros?: string[];
}
