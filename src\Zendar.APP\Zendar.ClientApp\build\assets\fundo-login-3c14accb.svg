<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 1920 1080">
  <defs>
    <style>
      .cls-1 {
        fill: none;
      }

      .cls-2 {
        isolation: isolate;
      }

      .cls-3 {
        clip-path: url(#clip-path);
      }

      .cls-4, .cls-9 {
        opacity: 0.43;
      }

      .cls-4 {
        fill: url(#radial-gradient);
      }

      .cls-5 {
        mix-blend-mode: screen;
        opacity: 0.16;
        fill: url(#radial-gradient-2);
      }

      .cls-6 {
        clip-path: url(#clip-path-2);
      }

      .cls-7 {
        fill: url(#linear-gradient);
      }

      .cls-8 {
        opacity: 0.15;
        fill: url(#radial-gradient-3);
      }

      .cls-9 {
        fill: url(#radial-gradient-4);
      }
    </style>
    <clipPath id="clip-path">
      <rect class="cls-1" x="-2134.7" width="1920" height="1080"/>
    </clipPath>
    <radialGradient id="radial-gradient" cx="-1102.07" cy="1082.15" r="1048.92" gradientTransform="matrix(-0.01, 1, -1.28, -0.01, 273.31, 2194.35)" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#4d1f8c" stop-opacity="0"/>
      <stop offset="0.1" stop-color="#491c88" stop-opacity="0.15"/>
      <stop offset="0.38" stop-color="#40147d" stop-opacity="0.52"/>
      <stop offset="0.63" stop-color="#390f75" stop-opacity="0.78"/>
      <stop offset="0.84" stop-color="#340b71" stop-opacity="0.94"/>
      <stop offset="1" stop-color="#330a6f"/>
    </radialGradient>
    <radialGradient id="radial-gradient-2" cx="-935.69" cy="-359.78" r="1147.44" gradientTransform="translate(-187.7 1192.3) scale(1.1 1)" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#522a9d" stop-opacity="0"/>
      <stop offset="0.96" stop-color="#000327"/>
    </radialGradient>
    <clipPath id="clip-path-2">
      <rect class="cls-1" width="1920" height="1080"/>
    </clipPath>
    <linearGradient id="linear-gradient" x1="1640.63" y1="-5128.18" x2="1641.76" y2="-5126.77" gradientTransform="matrix(1920, 0, 0, -1080, -3150419.68, -5536931.68)" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#390a71"/>
      <stop offset="1" stop-color="#6147c2"/>
    </linearGradient>
    <radialGradient id="radial-gradient-3" cx="1641.34" cy="-5127.05" r="0.78" gradientTransform="matrix(1920, 0, 0, -1180.44, -3150355.19, -6051098.99)" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#fbf29d"/>
      <stop offset="0.52" stop-color="#79577a"/>
      <stop offset="0.81" stop-color="#4a2973"/>
      <stop offset="1" stop-color="#2a0a6f"/>
    </radialGradient>
    <radialGradient id="radial-gradient-4" cx="1032.66" cy="1082.15" r="1048.92" gradientTransform="matrix(-0.01, 1, -1.28, -0.01, 2423.71, 59.67)" xlink:href="#radial-gradient"/>
  </defs>
  <g class="cls-2">
    <g id="submenu_-_textos" data-name="submenu - textos">
      <g class="cls-3">
        <rect class="cls-4" x="-2161.2" y="-1.2" width="2237.2" height="1339.84"/>
        <path class="cls-5" d="M19.1,1267.5c-230.9-256.4-678.8-430.3-1193.5-430.3-553.8,0-1030.1,201.3-1242.3,490v176.6H19.1Z"/>
      </g>
      <g class="cls-6">
        <rect id="Retângulo_7237" data-name="Retângulo 7237" class="cls-7" width="1920" height="1080"/>
        <rect id="Retângulo_7239" data-name="Retângulo 7239" class="cls-8" width="1920" height="1080"/>
        <rect class="cls-9" x="-26.4" y="-1.2" width="2237.2" height="1339.84"/>
      </g>
    </g>
  </g>
</svg>
