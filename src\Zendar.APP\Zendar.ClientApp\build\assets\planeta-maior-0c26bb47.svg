<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 1920 1080">
  <defs>
    <style>
      .cls-1 {
        fill: none;
      }

      .cls-2 {
        isolation: isolate;
      }

      .cls-3 {
        clip-path: url(#clip-path);
      }

      .cls-4, .cls-5, .cls-9 {
        mix-blend-mode: screen;
      }

      .cls-4, .cls-5 {
        opacity: 0.16;
      }

      .cls-4 {
        fill: url(#radial-gradient);
      }

      .cls-5 {
        fill: url(#radial-gradient-2);
      }

      .cls-6 {
        opacity: 0.7;
        fill: url(#radial-gradient-3);
      }

      .cls-7 {
        opacity: 0.45;
      }

      .cls-10, .cls-11, .cls-12, .cls-13, .cls-14, .cls-15, .cls-8 {
        opacity: 0.5;
      }

      .cls-8 {
        fill: url(#radial-gradient-4);
      }

      .cls-9 {
        fill: url(#radial-gradient-5);
      }

      .cls-10 {
        fill: url(#radial-gradient-6);
      }

      .cls-11 {
        fill: url(#radial-gradient-7);
      }

      .cls-12 {
        fill: url(#radial-gradient-8);
      }

      .cls-13 {
        fill: url(#radial-gradient-9);
      }

      .cls-14 {
        fill: url(#radial-gradient-10);
      }

      .cls-15 {
        fill: url(#radial-gradient-11);
      }

      .cls-16, .cls-17, .cls-18, .cls-19, .cls-20, .cls-21, .cls-22, .cls-23, .cls-24, .cls-25, .cls-26, .cls-27, .cls-28, .cls-29, .cls-30, .cls-31, .cls-32 {
        opacity: 0.25;
      }

      .cls-16 {
        fill: url(#radial-gradient-12);
      }

      .cls-17 {
        fill: url(#radial-gradient-13);
      }

      .cls-18 {
        fill: url(#radial-gradient-14);
      }

      .cls-19 {
        fill: url(#radial-gradient-15);
      }

      .cls-20 {
        fill: url(#radial-gradient-16);
      }

      .cls-21 {
        fill: url(#radial-gradient-17);
      }

      .cls-22 {
        fill: url(#radial-gradient-18);
      }

      .cls-23 {
        fill: url(#radial-gradient-19);
      }

      .cls-24 {
        fill: url(#radial-gradient-20);
      }

      .cls-25 {
        fill: url(#radial-gradient-21);
      }

      .cls-26 {
        fill: url(#radial-gradient-22);
      }

      .cls-27 {
        fill: url(#radial-gradient-23);
      }

      .cls-28 {
        fill: url(#radial-gradient-24);
      }

      .cls-29 {
        fill: url(#radial-gradient-25);
      }

      .cls-30 {
        fill: url(#radial-gradient-26);
      }

      .cls-31 {
        fill: url(#radial-gradient-27);
      }

      .cls-32 {
        fill: url(#radial-gradient-28);
      }
    </style>
    <clipPath id="clip-path">
      <rect class="cls-1" width="1920" height="1080"/>
    </clipPath>
    <radialGradient id="radial-gradient" cx="443.71" cy="-359.78" r="1147.44" gradientTransform="translate(433.7 1192.3) scale(1.1 1)" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#522a9d" stop-opacity="0"/>
      <stop offset="0.96" stop-color="#000327"/>
    </radialGradient>
    <radialGradient id="radial-gradient-2" cx="539.66" cy="-328.67" r="999.16" gradientTransform="translate(406.4 1139.1) scale(1.02 0.81)" xlink:href="#radial-gradient"/>
    <radialGradient id="radial-gradient-3" cx="962.71" cy="1093.35" r="586.69" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#643dac"/>
      <stop offset="0.34" stop-color="#643cac" stop-opacity="0.99"/>
      <stop offset="0.51" stop-color="#623bab" stop-opacity="0.94"/>
      <stop offset="0.65" stop-color="#6038aa" stop-opacity="0.87"/>
      <stop offset="0.77" stop-color="#5c33a8" stop-opacity="0.76"/>
      <stop offset="0.87" stop-color="#572da5" stop-opacity="0.62"/>
      <stop offset="0.97" stop-color="#5226a2" stop-opacity="0.46"/>
      <stop offset="1" stop-color="#5024a1" stop-opacity="0.4"/>
    </radialGradient>
    <radialGradient id="radial-gradient-4" cx="665.17" cy="913.95" r="397.08" gradientTransform="translate(190.8 189.3) scale(1.17 0.79)" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#522a9d"/>
      <stop offset="0.96" stop-color="#000327"/>
    </radialGradient>
    <radialGradient id="radial-gradient-5" cx="709.18" cy="1029.73" r="298.85" xlink:href="#radial-gradient-4"/>
    <radialGradient id="radial-gradient-6" cx="278.85" cy="-196.4" r="327.5" gradientTransform="translate(773.4 1031.8) scale(1.32 0.96)" xlink:href="#radial-gradient-4"/>
    <radialGradient id="radial-gradient-7" cx="278.85" cy="-196.4" r="327.5" gradientTransform="translate(773.4 1031.8) scale(1.32 0.96)" xlink:href="#radial-gradient-4"/>
    <radialGradient id="radial-gradient-8" cx="278.85" cy="-196.4" r="327.5" gradientTransform="translate(773.4 1031.8) scale(1.32 0.96)" xlink:href="#radial-gradient-4"/>
    <radialGradient id="radial-gradient-9" cx="278.85" cy="-196.4" r="327.5" gradientTransform="translate(773.4 1031.8) scale(1.32 0.96)" xlink:href="#radial-gradient-4"/>
    <radialGradient id="radial-gradient-10" cx="278.85" cy="-196.4" r="327.5" gradientTransform="translate(773.4 1031.8) scale(1.32 0.96)" xlink:href="#radial-gradient-4"/>
    <radialGradient id="radial-gradient-11" cx="278.85" cy="-196.4" r="327.5" gradientTransform="translate(773.4 1031.8) scale(1.32 0.96)" xlink:href="#radial-gradient-4"/>
    <radialGradient id="radial-gradient-12" cx="-152.44" cy="-37.98" r="99.93" gradientTransform="translate(772.7 1071.4) rotate(11.9) scale(1.32 0.96)" xlink:href="#radial-gradient-4"/>
    <radialGradient id="radial-gradient-13" cx="2033.76" cy="-2582.01" r="99.92" gradientTransform="matrix(-0.3, 1.22, -0.92, -0.48, -516.35, -2762.3)" xlink:href="#radial-gradient-4"/>
    <radialGradient id="radial-gradient-14" cx="2818.64" cy="-5413.54" r="99.93" gradientTransform="matrix(0.87, 0.72, -0.99, 0.63, -6216.74, 2388.76)" xlink:href="#radial-gradient-4"/>
    <radialGradient id="radial-gradient-15" cx="2081.48" cy="-2562.93" r="42.41" gradientTransform="matrix(1.19, 0.41, -0.56, 0.87, -2663.77, 2311.78)" xlink:href="#radial-gradient-4"/>
    <radialGradient id="radial-gradient-16" cx="2160.16" cy="-2521.05" r="42.41" gradientTransform="matrix(1.19, 0.41, -0.56, 0.87, -2663.77, 2311.78)" xlink:href="#radial-gradient-4"/>
    <radialGradient id="radial-gradient-17" cx="2186.68" cy="-2586.03" r="42.41" gradientTransform="matrix(1.19, 0.41, -0.56, 0.87, -2663.77, 2311.78)" xlink:href="#radial-gradient-4"/>
    <radialGradient id="radial-gradient-18" cx="2699.45" cy="-4161.33" r="42.41" gradientTransform="matrix(1.02, 0.61, -0.83, 0.75, -4760.12, 2495.01)" xlink:href="#radial-gradient-4"/>
    <radialGradient id="radial-gradient-19" cx="2493.61" cy="-3433.35" r="68.07" gradientTransform="matrix(1.11, 0.52, -0.71, 0.81, -3761.64, 2454.47)" xlink:href="#radial-gradient-4"/>
    <radialGradient id="radial-gradient-20" cx="2143.43" cy="-2643.16" r="68.07" gradientTransform="matrix(-0.3, 1.22, -0.92, -0.49, -533.81, -2885.07)" xlink:href="#radial-gradient-4"/>
    <radialGradient id="radial-gradient-21" cx="243.96" cy="-322.4" r="61.32" gradientTransform="matrix(1.32, 0.05, -0.07, 0.96, 403.84, 1250.07)" xlink:href="#radial-gradient-4"/>
    <radialGradient id="radial-gradient-22" cx="239.02" cy="-265.58" r="61.32" gradientTransform="matrix(1.32, 0.05, -0.07, 0.96, 403.84, 1250.07)" xlink:href="#radial-gradient-4"/>
    <radialGradient id="radial-gradient-23" cx="261.29" cy="-269.15" r="38.2" gradientTransform="matrix(1.32, 0.05, -0.07, 0.96, 403.84, 1250.07)" xlink:href="#radial-gradient-4"/>
    <radialGradient id="radial-gradient-24" cx="209.89" cy="-340.93" r="38.2" gradientTransform="matrix(1.32, 0.05, -0.07, 0.96, 403.84, 1250.07)" xlink:href="#radial-gradient-4"/>
    <radialGradient id="radial-gradient-25" cx="152.99" cy="-263.34" r="38.2" gradientTransform="matrix(1.32, 0.05, -0.07, 0.96, 403.84, 1250.07)" xlink:href="#radial-gradient-4"/>
    <radialGradient id="radial-gradient-26" cx="34" cy="-107.3" r="99.93" gradientTransform="matrix(1.29, 0.27, -0.2, 0.94, 764.19, 1019.15)" xlink:href="#radial-gradient-4"/>
    <radialGradient id="radial-gradient-27" cx="335.46" cy="-342.55" r="38.2" gradientTransform="matrix(1.32, 0.05, -0.07, 0.96, 403.84, 1250.07)" xlink:href="#radial-gradient-4"/>
    <radialGradient id="radial-gradient-28" cx="153.15" cy="-323.38" r="38.2" gradientTransform="matrix(1.32, 0.05, -0.07, 0.96, 403.84, 1250.07)" xlink:href="#radial-gradient-4"/>
  </defs>
  <g class="cls-2">
    <g id="submenu_-_textos" data-name="submenu - textos">
      <g class="cls-3">
        <g>
          <path class="cls-4" d="M2153.9,1267.5c-231-256.4-678.9-430.3-1193.6-430.3-553.7,0-1030.1,201.3-1242.3,490v176.6H2153.9Z"/>
          <path class="cls-5" d="M1958.7,1179.8C1771.4,998.5,1408,875.5,990.4,875.5c-449.3,0-835.7,142.3-1007.9,346.5v124.9H1958.7Z"/>
          <path class="cls-6" d="M1774.3,1143.6c-153.9-132.7-452.4-222.7-795.4-222.7-368.9,0-686.4,104.1-827.8,253.5v91.4H1774.3Z"/>
          <g class="cls-7">
            <path class="cls-8" d="M1345.2,1003.2c-2.5,12.2-39.3,18.4-82.3,13.7s-75.7-18.3-73.2-30.6,39.3-18.3,82.2-13.6S1347.7,991,1345.2,1003.2ZM552.7,986.7c-40.6,11.3-68.5,29.9-62.5,41.4s43.8,11.7,84.4.4S643,998.6,637,987.1,593.2,975.4,552.7,986.7Zm137.1,20c-29.2,7.6-49.6,20.5-45.5,28.8s31,8.9,60.2,1.3,49.6-20.6,45.5-28.9S719,999,689.8,1006.7Zm65.6-60.6c-22.1,5.1-37.8,14.3-35.1,20.6s22.8,7.2,44.9,2.2,37.7-14.3,35-20.6S777.4,941,755.4,946.1Zm708.4,71.9c-21.1-6.9-41.2-7.5-44.9-1.5s10.5,16.4,31.7,23.3,41.3,7.5,44.9,1.4S1485,1024.8,1463.8,1018Zm-100.5,25.8c-26.1-2.2-48.2,2.1-49.4,9.5s19,15.2,45.1,17.4,48.3-2,49.4-9.4S1389.4,1046,1363.3,1043.8Z"/>
            <path class="cls-9" d="M587,1027.7c-18.5,5.1-36.5,7.9-51.1,8.3a234.4,234.4,0,0,0,38.7-7.5c40.5-11.3,68.4-29.9,62.4-41.4-3.3-6.3-15.9-9.2-33.2-8.7,23.6-2.4,41.6.2,45.6,7.9C655.5,997.8,627.5,1016.3,587,1027.7Zm139.2-26.4c12.5-.1,21.6,2.1,23.8,6.6,4.1,8.3-16.3,21.2-45.5,28.9a196.1,196.1,0,0,1-24,4.5,158,158,0,0,0,36.4-5.4c29.2-7.6,49.6-20.5,45.5-28.8C759.5,1001.2,744.9,999.2,726.2,1001.3Zm545.7-28.6c43,4.6,75.8,18.3,73.3,30.5-1.5,7.2-14.9,12.3-34.4,14.3,26.1-.9,45.1-6.5,46.8-15.1,2.5-12.2-30.3-25.9-73.2-30.6a235,235,0,0,0-47.9-.6A256.6,256.6,0,0,1,1271.9,972.7Zm204.4,44.4c-16.1-5.2-31.6-6.8-39.7-4.6a114,114,0,0,1,27.2,5.5c21.2,6.8,35.4,17.2,31.7,23.2-.8,1.5-2.7,2.5-5.2,3.2,9,.5,15.7-.8,17.7-4C1511.6,1034.4,1497.4,1024,1476.3,1017.1ZM1375.7,1043a140.5,140.5,0,0,0-28.5.3,148.1,148.1,0,0,1,16.1.5c26.1,2.2,46.3,10,45.1,17.5-.7,4.4-8.8,7.7-20.8,9.1,18.5-.3,32.4-4.1,33.3-10C1422.1,1053,1401.9,1045.2,1375.7,1043ZM781.5,942.9c10,0,17.2,1.8,18.7,5.4,2.7,6.3-13,15.5-35,20.6-4.8,1-9.4,1.8-13.7,2.4a119.3,119.3,0,0,0,26.1-3.3c22.1-5,37.8-14.2,35.1-20.5S797.6,940.9,781.5,942.9Z"/>
            <g>
              <path class="cls-10" d="M1399.1,1068.1c-8.1-2.9-19.1-5.2-31.5-6.3-16.2-1.3-31-.2-40,2.7,8,2.9,19.1,5.2,31.4,6.2C1375.3,1072.1,1390,1071,1399.1,1068.1Z"/>
              <path class="cls-11" d="M1341.1,1008.9c-12.2-8-36.3-15.1-64.8-18.2-35.7-3.9-67.1-.3-78.1,8,12.1,8,36.2,15.1,64.7,18.2C1298.6,1020.8,1330,1017.2,1341.1,1008.9Z"/>
              <path class="cls-12" d="M1430.3,1031a112,112,0,0,0,20.3,8.8c15,4.8,29.5,6.5,37.9,5a105.4,105.4,0,0,0-20.3-8.8C1453.2,1031.1,1438.7,1029.4,1430.3,1031Z"/>
              <path class="cls-13" d="M498.1,1033.9c14.3,5.3,44.5,3.5,76.5-5.4,28-7.9,50-19.1,58.9-29.2-14.3-5.3-44.5-3.5-76.5,5.4C529,1012.6,507,1023.9,498.1,1033.9Z"/>
              <path class="cls-14" d="M759.7,964.1a109.7,109.7,0,0,0-23.2,7.9,115.3,115.3,0,0,0,28.7-3.1,104.1,104.1,0,0,0,23.2-8A120.8,120.8,0,0,0,759.7,964.1Z"/>
              <path class="cls-15" d="M655.5,1041.2c11.3,2,29.7.6,49-4.4,16.8-4.4,30.7-10.6,38.6-16.6-11.3-2-29.6-.6-49,4.5C677.3,1029.1,663.4,1035.2,655.5,1041.2Z"/>
            </g>
            <ellipse class="cls-16" cx="583.5" cy="1048" rx="30.4" ry="8.9" transform="translate(-204.2 143.4) rotate(-11.9)"/>
            <ellipse class="cls-17" cx="1194.1" cy="953" rx="8.8" ry="30.9" transform="translate(134.5 2053.7) rotate(-84.7)"/>
            <path class="cls-18" d="M1580.6,1052.9c-3.4,4.1-18.1,2.2-32.7-4.3s-23.7-15.2-20.3-19.3,18.1-2.1,32.7,4.4S1584.1,1048.8,1580.6,1052.9Z"/>
            <path class="cls-19" d="M1265.3,961.1c-.4,2.1-6.5,3.1-13.7,2.4s-12.8-3-12.4-5,6.5-3.1,13.8-2.3S1265.7,959.1,1265.3,961.1Z"/>
            <path class="cls-20" d="M1335.5,1029.9c-.4,2.1-6.5,3.1-13.8,2.4s-12.7-3-12.3-5,6.5-3.1,13.7-2.3S1335.9,1027.9,1335.5,1029.9Z"/>
            <path class="cls-21" d="M1403.7,984.3c-.4,2-6.5,3.1-13.7,2.4s-12.8-3-12.4-5,6.5-3.1,13.8-2.4S1404.1,982.3,1403.7,984.3Z"/>
            <path class="cls-22" d="M1461.1,1055.5c-1,1.9-7.3,1.8-14.1-.2s-11.4-5.1-10.3-7,7.3-1.8,14.1.2S1462.2,1053.6,1461.1,1055.5Z"/>
            <path class="cls-23" d="M1446.1,1000.9c-1.2,3.1-11.3,3.9-22.5,1.7s-19.5-6.6-18.3-9.8,11.2-3.9,22.5-1.7S1447.3,997.7,1446.1,1000.9Z"/>
            <ellipse class="cls-24" cx="1214.5" cy="1019.8" rx="6" ry="21" transform="translate(69.8 2117.3) rotate(-83.8)"/>
            <path class="cls-25" d="M770.6,982c1,2.9-6.5,6.7-16.7,8.6s-19.2,1.2-20.2-1.7,6.4-6.7,16.6-8.6S769.6,979.1,770.6,982Z"/>
            <path class="cls-26" d="M760.1,1036.3c1,2.9-6.5,6.8-16.7,8.7s-19.2,1.1-20.2-1.8,6.4-6.7,16.6-8.6S759.1,1033.5,760.1,1036.3Z"/>
            <path class="cls-27" d="M781,1023.1c.7,1.8-4,4.2-10.3,5.3s-12,.8-12.7-1,4.1-4.2,10.4-5.4S780.4,1021.3,781,1023.1Z"/>
            <path class="cls-28" d="M718.4,951.4c.7,1.8-4,4.2-10.3,5.4s-12,.7-12.7-1.1,4-4.1,10.4-5.3S717.8,949.7,718.4,951.4Z"/>
            <path class="cls-29" d="M638.1,1023.1c.6,1.8-4.1,4.2-10.4,5.3s-12,.8-12.6-1,4-4.2,10.3-5.4S637.4,1021.3,638.1,1023.1Z"/>
            <ellipse class="cls-30" cx="829.3" cy="981.3" rx="30.4" ry="8.9" transform="translate(-185.1 192.8) rotate(-11.9)"/>
            <path class="cls-31" d="M883.8,956.4c.7,1.7-4,4.2-10.3,5.3s-12,.8-12.7-1,4.1-4.2,10.4-5.4S883.2,954.6,883.8,956.4Z"/>
            <path class="cls-32" d="M642.5,965.4c.6,1.8-4,4.2-10.4,5.4s-12,.7-12.6-1.1,4-4.2,10.4-5.4S641.9,963.6,642.5,965.4Z"/>
          </g>
        </g>
      </g>
    </g>
  </g>
</svg>
