import { FluxoCaixaData, FluxoCaixaResponseCompleta } from './types';

// Dados mocados completos para demonstração do Fluxo de Caixa
export const fluxoCaixaMockData: FluxoCaixaData = {
  saldoInicial: {
    id: 'saldo-inicial',
    nome: 'Saldo Inicial',
    tipo: 'categoria',
    nivel: 0,
    valores: {
      jan: 994285.87,
      fev: 1011226.45,
      mar: 1106531.49,
      abr: 1193451.00,
      mai: 1192375.45,
      jun: 1232643.53,
      jul: 1423186.00,
      ago: 1219543.16,
      set: 1390592.21,
      out: 1604889.93,
      nov: 1650000.00,
      dez: 1700000.00,
      total: 994285.87,
    },
    cor: 'blue.500',
  },
  entradas: {
    id: 'entradas',
    nome: 'Entrada',
    tipo: 'categoria',
    nivel: 0,
    valores: {
      jan: 673296.43,
      fev: 659490.56,
      mar: 668941.26,
      abr: 676922.23,
      mai: 719331.37,
      jun: 729372.54,
      jul: 775526.95,
      ago: 749005.98,
      set: 764745.72,
      out: 780000.00,
      nov: 820000.00,
      dez: 850000.00,
      total: 8866633.04,
    },
    expandido: true,
    cor: 'green.500',
    filhos: [
      {
        id: 'receitas-vendas',
        nome: 'RECEITAS DE VENDAS',
        tipo: 'subcategoria',
        nivel: 1,
        valores: {
          jan: 768.00,
          fev: 2921.60,
          mar: 3146.20,
          abr: 1848.40,
          mai: 982.00,
          jun: 4349.70,
          jul: 2651.60,
          ago: 1385.20,
          set: 2093.60,
          out: 2500.00,
          nov: 3000.00,
          dez: 3500.00,
          total: 29146.30,
        },
        filhos: [
          {
            id: 'venda-produtos',
            nome: 'VENDA DE PRODUTOS',
            tipo: 'item',
            nivel: 2,
            planoContaId: '001.001.001',
            codigo: '3.1.1',
            valores: {
              jan: 768.00,
              fev: 2921.60,
              mar: 3146.20,
              abr: 1848.40,
              mai: 982.00,
              jun: 4349.70,
              jul: 2651.60,
              ago: 1385.20,
              set: 2093.60,
              out: 2500.00,
              nov: 3000.00,
              dez: 3500.00,
              total: 29146.30,
            },
          },
        ],
      },
      {
        id: 'receitas-financeiras',
        nome: 'RECEITAS FINANCEIRAS',
        tipo: 'subcategoria',
        nivel: 1,
        valores: {
          jan: 14868.49,
          fev: 15080.91,
          mar: 15407.11,
          abr: 17908.47,
          mai: 18173.76,
          jun: 19575.56,
          jul: 23069.01,
          ago: 19049.11,
          set: 21612.43,
          out: 22000.00,
          nov: 23000.00,
          dez: 24000.00,
          total: 233744.85,
        },
        filhos: [
          {
            id: 'juros-recebidos',
            nome: 'JUROS RECEBIDOS DE CLIENTES',
            tipo: 'item',
            nivel: 2,
            planoContaId: '001.002.001',
            codigo: '3.2.1',
            valores: {
              jan: 9802.90,
              fev: 8453.97,
              mar: 8860.11,
              abr: 9708.80,
              mai: 9900.51,
              jun: 9901.91,
              jul: 11048.33,
              ago: 9493.91,
              set: 10154.79,
              out: 10500.00,
              nov: 11000.00,
              dez: 11500.00,
              total: 120325.23,
            },
          },
          {
            id: 'outras-receitas',
            nome: 'OUTRAS RECEITAS FINANCEIRAS',
            tipo: 'item',
            nivel: 2,
            planoContaId: '001.002.002',
            codigo: '3.2.2',
            valores: {
              jan: 303.06,
              fev: 0.04,
              mar: 169.83,
              abr: 0,
              mai: 0,
              jun: 0.05,
              jul: 444.03,
              ago: 0.04,
              set: 0.05,
              out: 100.00,
              nov: 200.00,
              dez: 300.00,
              total: 1517.10,
            },
          },
          {
            id: 'rendimentos',
            nome: 'RENDIMENTOS S/ APLICAÇÕES',
            tipo: 'item',
            nivel: 2,
            planoContaId: '001.002.003',
            codigo: '3.2.3',
            valores: {
              jan: 4762.53,
              fev: 6626.90,
              mar: 6377.17,
              abr: 8199.67,
              mai: 8273.25,
              jun: 9673.60,
              jul: 11576.65,
              ago: 9555.16,
              set: 11457.59,
              out: 11400.00,
              nov: 11800.00,
              dez: 12200.00,
              total: 111902.52,
            },
          },
        ],
      },
      {
        id: 'servicos-recorrentes',
        nome: 'SERVIÇOS RECORRENTES POWERSTOCK',
        tipo: 'subcategoria',
        nivel: 1,
        valores: {
          jan: 656689.94,
          fev: 640878.05,
          mar: 648917.95,
          abr: 656055.36,
          mai: 699135.61,
          jun: 704407.28,
          jul: 749266.34,
          ago: 728571.67,
          set: 741039.69,
          out: 755000.00,
          nov: 785000.00,
          dez: 815000.00,
          total: 8579967.89,
        },
        filhos: [
          {
            id: 'mensalidade-software',
            nome: 'MENSALIDADE DE SOFTWARE',
            tipo: 'item',
            nivel: 2,
            planoContaId: '001.003.001',
            codigo: '3.3.1',
            valores: {
              jan: 656689.94,
              fev: 640878.05,
              mar: 648917.95,
              abr: 656055.36,
              mai: 699135.61,
              jun: 704407.28,
              jul: 749266.34,
              ago: 728571.67,
              set: 741039.69,
              out: 755000.00,
              nov: 785000.00,
              dez: 815000.00,
              total: 8579967.89,
            },
          },
        ],
      },
      {
        id: 'venda-imobilizado',
        nome: 'VENDA DE IMOBILIZADO',
        tipo: 'subcategoria',
        nivel: 1,
        valores: {
          jan: 970.00,
          fev: 610.00,
          mar: 1470.00,
          abr: 1110.00,
          mai: 1040.00,
          jun: 1040.00,
          jul: 540.00,
          ago: 0,
          set: 0,
          out: 0,
          nov: 0,
          dez: 0,
          total: 6780.00,
        },
      },
    ],
  },
  saidas: {
    id: 'saidas',
    nome: 'Saída',
    tipo: 'categoria',
    nivel: 0,
    valores: {
      jan: -656355.85,
      fev: -564185.52,
      mar: -582021.75,
      abr: -677703.88,
      mai: -679068.14,
      jun: -538830.07,
      jul: -978919.79,
      ago: -578206.93,
      set: -550448.00,
      out: -600000.00,
      nov: -650000.00,
      dez: -700000.00,
      total: -7755739.93,
    },
    cor: 'red.500',
  },
  transferencias: {
    id: 'transferencias',
    nome: 'Transferências',
    tipo: 'categoria',
    nivel: 0,
    valores: {
      jan: 0.00,
      fev: 0.00,
      mar: 0.00,
      abr: -293.90,
      mai: 4.85,
      jun: 0.00,
      jul: -250.00,
      ago: 250.00,
      set: 0.00,
      out: 0.00,
      nov: 0.00,
      dez: 0.00,
      total: -289.05,
    },
    cor: 'orange.500',
  },
  resultadoPeriodo: {
    id: 'resultado-periodo',
    nome: 'Resultado Período',
    tipo: 'categoria',
    nivel: 0,
    valores: {
      jan: 16940.58,
      fev: 95305.04,
      mar: 86919.51,
      abr: -1075.55,
      mai: 40268.08,
      jun: 190542.47,
      jul: -203642.84,
      ago: 171049.05,
      set: 214297.72,
      out: 180000.00,
      nov: 170000.00,
      dez: 150000.00,
      total: 1110604.06,
    },
    cor: 'purple.500',
  },
  saldoFinal: {
    id: 'saldo-final',
    nome: 'Saldo Final',
    tipo: 'categoria',
    nivel: 0,
    valores: {
      jan: 1011226.45,
      fev: 1106531.49,
      mar: 1193451.00,
      abr: 1192375.45,
      mai: 1232643.53,
      jun: 1423186.00,
      jul: 1219543.16,
      ago: 1390592.21,
      set: 1604889.93,
      out: 1784889.93,
      nov: 1954889.93,
      dez: 2104889.93,
      total: 2104889.93,
    },
    cor: 'teal.500',
  },
};

// Resposta completa da API com metadados
export const fluxoCaixaResponseMock: FluxoCaixaResponseCompleta = {
  sucesso: true,
  dados: fluxoCaixaMockData,
  calculos: {
    totalEntradas: 8866633.04,
    totalSaidas: -7755739.93,
    saldoLiquido: 1110893.11,
    variacao: 111.7,
    percentualVariacao: 12.5,
  },
  metadata: {
    dataUltimaAtualizacao: '2024-10-06T15:30:00Z',
    totalRegistros: 15,
    tempoProcessamento: 245,
    versaoApi: '2.1.0',
  },
  avisos: [
    'Alguns valores podem estar sujeitos a ajustes contábeis',
    'Dados atualizados até o último fechamento contábil',
  ],
};
