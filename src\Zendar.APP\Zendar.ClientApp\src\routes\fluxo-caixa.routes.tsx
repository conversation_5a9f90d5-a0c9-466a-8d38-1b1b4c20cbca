import React from 'react';

import LayoutGuard from './LayoutGuard';
import ConstanteRotas from '../constants/rotas';
import ConstanteFuncionalidades from '../constants/permissoes';

import FluxoCaixaListar from '../pages/FluxoCaixa/Listar';

const FluxoCaixaRoutes = [
  <LayoutGuard
    key="fluxocaixa-listar"
    permissaoFuncionalidade={ConstanteFuncionalidades.FLUXO_CAIXA_LISTAR}
    component={FluxoCaixaListar}
    breadcrumb={[
      {
        title: 'Financeiro',
      },
      {
        title: 'Fluxo de Caixa',
      },
    ]}
    path={ConstanteRotas.FLUXO_CAIXA}
    exact
    meta={{ auth: true }}
  />,
];

export default FluxoCaixaRoutes;
