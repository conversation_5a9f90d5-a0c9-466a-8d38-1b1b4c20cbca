<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 1920 1080">
  <defs>
    <style>
      .cls-1 {
        fill: none;
      }

      .cls-2 {
        clip-path: url(#clip-path);
      }

      .cls-3, .cls-63 {
        opacity: 0.43;
      }

      .cls-3 {
        fill: url(#radial-gradient);
      }

      .cls-4 {
        opacity: 0.7;
        fill: url(#radial-gradient-2);
      }

      .cls-5 {
        fill: #5935ab;
      }

      .cls-6 {
        fill: #6a3ab2;
      }

      .cls-10, .cls-14, .cls-15, .cls-16, .cls-23, .cls-25, .cls-27, .cls-29, .cls-33, .cls-36, .cls-37, .cls-38, .cls-39, .cls-45, .cls-46, .cls-47, .cls-50, .cls-53, .cls-55, .cls-56, .cls-57, .cls-60, .cls-7, .cls-9 {
        isolation: isolate;
      }

      .cls-11, .cls-7 {
        opacity: 0.14;
      }

      .cls-7 {
        fill: url(#radial-gradient-3);
      }

      .cls-31, .cls-33, .cls-8 {
        fill: #fff;
      }

      .cls-8 {
        opacity: 0.03;
      }

      .cls-9 {
        fill: #7340b4;
      }

      .cls-10, .cls-16, .cls-25, .cls-33, .cls-36, .cls-40, .cls-45, .cls-53, .cls-9 {
        opacity: 0.45;
      }

      .cls-10 {
        fill: #875fc0;
      }

      .cls-11 {
        fill: url(#radial-gradient-4);
      }

      .cls-12 {
        fill: #643799;
      }

      .cls-13 {
        fill: #5a1796;
      }

      .cls-14, .cls-29 {
        opacity: 0.25;
      }

      .cls-14 {
        fill: url(#radial-gradient-5);
      }

      .cls-15 {
        opacity: 0.07;
        fill: url(#radial-gradient-6);
      }

      .cls-16 {
        fill: #5d1b99;
      }

      .cls-17 {
        fill: #623997;
        opacity: 0.64;
      }

      .cls-18 {
        fill: #6c3ab8;
      }

      .cls-19 {
        fill: #552aaf;
      }

      .cls-20 {
        opacity: 0.39;
        fill: url(#radial-gradient-7);
      }

      .cls-21 {
        fill: #5033a8;
      }

      .cls-22 {
        fill: #6845b2;
      }

      .cls-23 {
        opacity: 0.19;
        fill: url(#radial-gradient-8);
      }

      .cls-24 {
        fill: #6c4ab4;
      }

      .cls-25 {
        fill: #5730a9;
      }

      .cls-26 {
        fill: #4b1b80;
      }

      .cls-27 {
        opacity: 0.6;
      }

      .cls-28, .cls-53 {
        fill: #6b3f9c;
      }

      .cls-29 {
        fill: url(#radial-gradient-9);
      }

      .cls-30 {
        fill: #43117a;
      }

      .cls-31, .cls-32 {
        opacity: 0.12;
      }

      .cls-34 {
        opacity: 0.32;
        fill: url(#radial-gradient-10);
      }

      .cls-35 {
        fill: #6930aa;
      }

      .cls-36 {
        fill: #6e38a9;
      }

      .cls-37 {
        fill: #5f30a8;
      }

      .cls-37, .cls-38, .cls-39, .cls-46, .cls-47, .cls-55, .cls-56, .cls-57, .cls-60 {
        opacity: 0.5;
      }

      .cls-38 {
        fill: #522d9f;
      }

      .cls-39, .cls-41 {
        fill: #572da1;
      }

      .cls-42 {
        fill: #662ba5;
      }

      .cls-43 {
        fill: #5a2097;
      }

      .cls-44 {
        fill: #4e269e;
      }

      .cls-45 {
        fill: #6440ad;
      }

      .cls-46, .cls-48 {
        fill: #3c158e;
      }

      .cls-49 {
        fill: #60368d;
      }

      .cls-50 {
        fill: #592a85;
      }

      .cls-51 {
        fill: #612f95;
      }

      .cls-52 {
        fill: #521b8b;
      }

      .cls-54 {
        fill: #56267f;
      }

      .cls-55 {
        fill: url(#radial-gradient-11);
      }

      .cls-56 {
        fill: url(#radial-gradient-12);
      }

      .cls-57 {
        fill: url(#radial-gradient-13);
      }

      .cls-58 {
        fill: #5f3286;
      }

      .cls-59 {
        fill: #6c43af;
      }

      .cls-60, .cls-61 {
        fill: #5f32ae;
      }

      .cls-62 {
        fill: #6d45b0;
      }

      .cls-63 {
        fill: url(#radial-gradient-14);
      }
    </style>
    <clipPath id="clip-path">
      <rect class="cls-1" x="-2137.6" width="1920" height="1080"/>
    </clipPath>
    <radialGradient id="radial-gradient" cx="-1104.89" cy="1082.15" r="1048.92" gradientTransform="matrix(-0.01, 1, -1.28, -0.01, 270.48, 2197.16)" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#4d1f8c" stop-opacity="0"/>
      <stop offset="0.1" stop-color="#491c88" stop-opacity="0.15"/>
      <stop offset="0.38" stop-color="#40147d" stop-opacity="0.52"/>
      <stop offset="0.63" stop-color="#390f75" stop-opacity="0.78"/>
      <stop offset="0.84" stop-color="#340b71" stop-opacity="0.94"/>
      <stop offset="1" stop-color="#330a6f"/>
    </radialGradient>
    <radialGradient id="radial-gradient-2" cx="-491.77" cy="-5162.54" r="0.49" gradientTransform="matrix(137.62, 0, 0, -137.62, 69318.27, -709794.54)" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#8013a7"/>
      <stop offset="0.26" stop-color="#8014a7" stop-opacity="0.99"/>
      <stop offset="0.41" stop-color="#7f17a7" stop-opacity="0.94"/>
      <stop offset="0.54" stop-color="#7e1ba7" stop-opacity="0.85"/>
      <stop offset="0.65" stop-color="#7c22a8" stop-opacity="0.73"/>
      <stop offset="0.75" stop-color="#7a2aa8" stop-opacity="0.57"/>
      <stop offset="0.84" stop-color="#7835a8" stop-opacity="0.38"/>
      <stop offset="0.93" stop-color="#7541a9" stop-opacity="0.16"/>
      <stop offset="0.98" stop-color="#734aa9" stop-opacity="0"/>
    </radialGradient>
    <radialGradient id="radial-gradient-3" cx="-490.68" cy="-5175.45" r="1.51" gradientTransform="matrix(103.41, 0, 0, -103.38, 52372.32, -534419.11)" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#4a2e98"/>
      <stop offset="0.96" stop-color="#000a2a"/>
    </radialGradient>
    <radialGradient id="radial-gradient-4" cx="-492.57" cy="-5153.6" r="0.49" gradientTransform="matrix(176.75, 0, 0, -176.75, 87164.18, -910679.47)" gradientUnits="userSpaceOnUse">
      <stop offset="0.05" stop-color="#dea136"/>
      <stop offset="0.41" stop-color="#dda037" stop-opacity="0.99"/>
      <stop offset="0.55" stop-color="#dc9c39" stop-opacity="0.96"/>
      <stop offset="0.64" stop-color="#db963e" stop-opacity="0.91"/>
      <stop offset="0.71" stop-color="#d98d46" stop-opacity="0.83"/>
      <stop offset="0.78" stop-color="#d78251" stop-opacity="0.73"/>
      <stop offset="0.83" stop-color="#d5755f" stop-opacity="0.61"/>
      <stop offset="0.89" stop-color="#d26572" stop-opacity="0.47"/>
      <stop offset="0.93" stop-color="#cf5388" stop-opacity="0.3"/>
      <stop offset="0.98" stop-color="#cb3fa1" stop-opacity="0.12"/>
      <stop offset="1" stop-color="#c935b1" stop-opacity="0"/>
    </radialGradient>
    <radialGradient id="radial-gradient-5" cx="-457.56" cy="-5118.86" r="1.35" gradientTransform="matrix(0.4, 149.07, 149.11, -0.45, 762689.92, 66347.07)" gradientUnits="userSpaceOnUse">
      <stop offset="0.05" stop-color="#dea136"/>
      <stop offset="1" stop-color="#7135b1"/>
    </radialGradient>
    <radialGradient id="radial-gradient-6" cx="-454.22" cy="-5118.59" r="1.21" gradientTransform="matrix(0.41, 136.83, 136.85, -0.41, 699909.87, 60582.79)" gradientUnits="userSpaceOnUse">
      <stop offset="0.05" stop-color="#dea136"/>
      <stop offset="1" stop-color="#e7a5dc"/>
    </radialGradient>
    <radialGradient id="radial-gradient-7" cx="-491.09" cy="-5169.99" r="0.5" gradientTransform="matrix(116.2, 0, 0, -116.2, 58275.61, -600634.61)" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#3f237b"/>
      <stop offset="0.22" stop-color="#40277d" stop-opacity="0.97"/>
      <stop offset="0.41" stop-color="#443385" stop-opacity="0.89"/>
      <stop offset="0.57" stop-color="#4a4792" stop-opacity="0.74"/>
      <stop offset="0.73" stop-color="#5263a4" stop-opacity="0.54"/>
      <stop offset="0.88" stop-color="#5d87bb" stop-opacity="0.28"/>
      <stop offset="0.99" stop-color="#66a7cf" stop-opacity="0.05"/>
    </radialGradient>
    <radialGradient id="radial-gradient-8" cx="-542.59" cy="-5162.69" r="1.33" gradientTransform="matrix(53.24, -72.92, -72.93, -53.25, -347043.61, -315667.11)" gradientUnits="userSpaceOnUse">
      <stop offset="0.06" stop-color="#69b0d5"/>
      <stop offset="1" stop-color="#3f237b"/>
    </radialGradient>
    <radialGradient id="radial-gradient-9" cx="-387.4" cy="-5339.04" r="1.81" gradientTransform="matrix(21.42, 8.34, 8.36, -21.42, 52670.86, -110665.5)" xlink:href="#radial-gradient-8"/>
    <radialGradient id="radial-gradient-10" cx="-487.6" cy="-5209.77" r="0.5" gradientTransform="matrix(63.44, 0, 0, -63.44, 32678.18, -330123.94)" gradientUnits="userSpaceOnUse">
      <stop offset="0.05" stop-color="#dea136"/>
      <stop offset="0.31" stop-color="#dea037" stop-opacity="0.99"/>
      <stop offset="0.44" stop-color="#dd9c3b" stop-opacity="0.96"/>
      <stop offset="0.54" stop-color="#dc9643" stop-opacity="0.9"/>
      <stop offset="0.62" stop-color="#da8d4d" stop-opacity="0.81"/>
      <stop offset="0.69" stop-color="#d8815a" stop-opacity="0.7"/>
      <stop offset="0.76" stop-color="#d5726b" stop-opacity="0.57"/>
      <stop offset="0.82" stop-color="#d2617f" stop-opacity="0.41"/>
      <stop offset="0.88" stop-color="#ce4d95" stop-opacity="0.22"/>
      <stop offset="0.94" stop-color="#c937ae" stop-opacity="0.02"/>
      <stop offset="0.94" stop-color="#c935b1" stop-opacity="0"/>
    </radialGradient>
    <radialGradient id="radial-gradient-11" cx="-485.87" cy="-5452.6" r="2.6" gradientTransform="matrix(16.74, -0.96, -0.96, -16.75, 3367.83, -91702.3)" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#ee1375"/>
      <stop offset="0.14" stop-color="#e91477"/>
      <stop offset="0.31" stop-color="#db197c"/>
      <stop offset="0.49" stop-color="#c32185"/>
      <stop offset="0.67" stop-color="#a23092"/>
      <stop offset="0.87" stop-color="#7a45a5"/>
      <stop offset="0.9" stop-color="#734aa9"/>
    </radialGradient>
    <radialGradient id="radial-gradient-12" cx="-462.56" cy="-6171.17" r="2.76" gradientTransform="matrix(5.31, -0.3, -0.3, -5.3, 1102.85, -32728.77)" xlink:href="#radial-gradient-11"/>
    <radialGradient id="radial-gradient-13" cx="-479.82" cy="-5638.66" r="2.82" gradientTransform="matrix(10.74, -0.61, -0.61, -10.74, 2216.5, -60746.55)" xlink:href="#radial-gradient-11"/>
    <radialGradient id="radial-gradient-14" cx="1801.18" cy="176.2" r="30.28" gradientTransform="matrix(1, 0, 0, 1, 0, 0)" xlink:href="#radial-gradient"/>
  </defs>
  <g id="submenu_-_textos" data-name="submenu - textos">
    <g class="cls-2">
      <rect class="cls-3" x="-2164" y="-1.2" width="2237.2" height="1339.84"/>
    </g>
    <g>
      <g>
        <circle id="Elipse_374" data-name="Elipse 374" class="cls-4" cx="1640.5" cy="663.2" r="68.8"/>
        <circle id="Elipse_376" data-name="Elipse 376" class="cls-5" cx="1640.5" cy="664.9" r="58.8"/>
        <g id="Grupo_5698" data-name="Grupo 5698">
          <path id="Caminho_9599" data-name="Caminho 9599" class="cls-6" d="M1665.4,687.5a3.5,3.5,0,0,0,.8-2.6c-.2-1.8-2.4-2.7-4.2-2.7-4.4,0-8.3,2.3-11.8,4.9s-6.8,5.6-10.9,7.1-9.3,1-11.9-2.4-1.9-8.9-3.8-13a3.3,3.3,0,0,0-1.1-1.5c-1.2-.8-2.8.1-4.1,1-6.9,4.7-14.7,8.7-23.1,9.6a27.8,27.8,0,0,1-9.1-.3,58.8,58.8,0,0,0,22.5,26.8,6.6,6.6,0,0,1,.6-2c3.1-6.6,11.9-7.7,19.3-8.3s14-1.4,20.6-3.9S1661.8,693.5,1665.4,687.5Z"/>
          <path id="Caminho_9600" data-name="Caminho 9600" class="cls-6" d="M1698.9,672c-4,.1-7.8.4-11.1,2.5-5.4,3.5-7,10.9-6.1,17.3.2,1.7.5,3.5-.2,5a5.9,5.9,0,0,1-2,2.4c-7.4,6.4-16.9,9.7-25.4,14.7-3.8,2.1-7.7,5.4-7.7,9.6A58.9,58.9,0,0,0,1698.9,672Z"/>
          <path id="Caminho_9601" data-name="Caminho 9601" class="cls-6" d="M1581.7,664.9v1a9.9,9.9,0,0,0,3.2,1.7c2.6.9,5.5,1,7.6,2.7s1.9,2.4,3.1,3.3c2.8,2.4,7.3,1.6,10.2-.7s4.6-5.8,6.2-9.2,3.5-6.7,6.5-8.9a11.9,11.9,0,0,1,8.8-1.8c2.6.5,5.2,2,7.9,1.7s6-4.1,6.1-7.7-1.6-6.9-3.6-9.8a20.7,20.7,0,0,1-2.7-4.5c-1.6-4.4,1.1-9.3,4.4-12.7s7.3-6.2,9.5-10.4a8.5,8.5,0,0,0,1-2.8,59,59,0,0,0-67.5,48.8,58.2,58.2,0,0,0-.7,9.3Z"/>
          <path id="Caminho_9602" data-name="Caminho 9602" class="cls-6" d="M1664.8,620.8c1,3.5,3.2,6.6,4.8,9.9s2.6,7.3,1,10.6-6.7,5.7-10.8,7.6a43,43,0,0,0-10.3,6.6c-3.4,2.9-6.5,7-5.8,11.5s5.6,7.5,10.1,8.7c2.9.7,6.1,1.1,8.7-.2,5.1-2.5,6.5-9.6,11.5-12s8.9-.2,13.4.2a21.1,21.1,0,0,0,11.8-2.6,58.9,58.9,0,0,0-34.3-49.7A15.9,15.9,0,0,0,1664.8,620.8Z"/>
        </g>
        <path id="Caminho_9603" data-name="Caminho 9603" class="cls-7" d="M1679,620.4a58.9,58.9,0,0,1-95.6,58.8,58.9,58.9,0,1,0,95.6-58.8Z"/>
        <path id="Caminho_9604" data-name="Caminho 9604" class="cls-8" d="M1689.2,640.3a54.7,54.7,0,0,1-100.3,43.2,54.7,54.7,0,1,0,103.3-36.1A44.5,44.5,0,0,0,1689.2,640.3Z"/>
        <path id="Caminho_9605" data-name="Caminho 9605" class="cls-9" d="M1585.3,670.1a58.8,58.8,0,0,1,105.2-36.2,58.8,58.8,0,0,0-100,62,48.7,48.7,0,0,0,3.7,5.2A58.5,58.5,0,0,1,1585.3,670.1Z"/>
        <path id="Caminho_9606" data-name="Caminho 9606" class="cls-8" d="M1590.4,676.8c-1.8-7,1-17.6,2.4-22.7a56.4,56.4,0,0,1,20.2-30.2c26.1-19.5,56.3-5.2,64.3,3.5s-1.4,18.1-15.9,17.9-28.7-.5-37,6.9-7.5,25.9-18.5,29.8c-3.7,1.3-9,2.6-12.4,0A9.4,9.4,0,0,1,1590.4,676.8Z"/>
        <g id="Grupo_5700" data-name="Grupo 5700">
          <path id="Caminho_9607" data-name="Caminho 9607" class="cls-10" d="M1616.5,631.7c3.2,2.8,1.7,9.6-3.2,15.3s-11.6,8-14.8,5.1-1.8-9.6,3.2-15.3S1613.3,628.9,1616.5,631.7Z"/>
          <path id="Caminho_9608" data-name="Caminho 9608" class="cls-10" d="M1633.5,620.7c1.1,2.2-1.1,5.7-5,7.8s-8.1,1.9-9.2-.3,1-5.6,5-7.7S1632.3,618.5,1633.5,620.7Z"/>
        </g>
      </g>
      <g>
        <circle id="Elipse_377" data-name="Elipse 377" class="cls-11" cx="103.4" cy="209.7" r="88.4"/>
        <circle id="Elipse_378" data-name="Elipse 378" class="cls-12" cx="103.4" cy="209.7" r="75.7"/>
        <g id="Grupo_5702" data-name="Grupo 5702">
          <path id="Caminho_9609" data-name="Caminho 9609" class="cls-13" d="M74.4,241.8a5.9,5.9,0,0,0,3.4,1c2.4-.3,3.4-3.1,3.5-5.5,0-5.6-3.1-10.7-6.4-15.1s-7.2-8.7-9.1-14-1.4-11.9,3-15.3,11.4-2.5,16.7-4.9a4.5,4.5,0,0,0,1.9-1.4c1-1.7-.2-3.7-1.2-5.3-6.2-9-11.3-19-12.6-29.8a41.1,41.1,0,0,1,.4-11.6,75.8,75.8,0,0,0-34.3,29,8.1,8.1,0,0,1,2.5.8c8.6,4,10,15.3,10.8,24.8s1.8,18,5,26.4S66.7,237.2,74.4,241.8Z"/>
          <path id="Caminho_9610" data-name="Caminho 9610" class="cls-13" d="M94.5,284.8c-.1-5.1-.5-10.1-3.2-14.3-4.6-7-14.1-8.9-22.4-7.8-2.1.3-4.4.7-6.4-.2a9.1,9.1,0,0,1-3-2.5c-8.3-9.6-12.6-21.8-19-32.7-2.8-4.8-7-9.9-12.4-9.9A75.8,75.8,0,0,0,94.5,284.8Z"/>
          <path id="Caminho_9611" data-name="Caminho 9611" class="cls-13" d="M103.2,134h-1.3a12.5,12.5,0,0,0-2.2,4.1c-1.1,3.3-1.2,7.2-3.4,9.9s-3,2.4-4.2,3.9c-3.1,3.6-2.1,9.5.9,13.2s7.5,5.8,11.8,7.9,8.8,4.4,11.5,8.3a14.7,14.7,0,0,1,2.4,11.3c-.7,3.4-2.6,6.7-2.1,10.2s5.3,7.7,9.8,7.8,8.9-2.1,12.6-4.7a26.3,26.3,0,0,1,5.8-3.5c5.7-2,12,1.4,16.4,5.6s8,9.4,13.4,12.3a10.9,10.9,0,0,0,3.6,1.2,75.7,75.7,0,0,0-63-86.6,80.8,80.8,0,0,0-12-.9Z"/>
          <path id="Caminho_9612" data-name="Caminho 9612" class="cls-13" d="M160.3,240.7c-4.5,1.4-8.5,4.2-12.7,6.3s-9.4,3.3-13.7,1.3-7.4-8.7-9.8-14a58.8,58.8,0,0,0-8.5-13.1c-3.8-4.3-9.1-8.4-14.8-7.4s-9.6,7.2-11.2,13c-.9,3.7-1.3,7.8.3,11.2,3.2,6.5,12.3,8.3,15.5,14.8s.2,11.3-.2,17.1a28.5,28.5,0,0,0,3.3,15.2,75.5,75.5,0,0,0,63.9-44.3A21.6,21.6,0,0,0,160.3,240.7Z"/>
        </g>
        <path id="Caminho_9613" data-name="Caminho 9613" class="cls-14" d="M161,258.6A75.7,75.7,0,0,1,85,135.9a75.7,75.7,0,1,0,76,122.7Z"/>
        <path id="Caminho_9614" data-name="Caminho 9614" class="cls-15" d="M135.3,272.2a70.5,70.5,0,0,1-56-128.9,70.4,70.4,0,1,0,46.9,132.8A80.3,80.3,0,0,0,135.3,272.2Z"/>
        <path id="Caminho_9615" data-name="Caminho 9615" class="cls-16" d="M96.5,138.7a75.7,75.7,0,0,1,47,135.2,75.7,75.7,0,0,0-80-128.5,46.3,46.3,0,0,0-6.8,4.8A74.7,74.7,0,0,1,96.5,138.7Z"/>
        <path id="Caminho_9616" data-name="Caminho 9616" class="cls-17" d="M88,145.2c9-2.3,22.6,1.2,29.1,3,15.4,4.2,29.3,13,38.9,25.9,25.2,33.5,7,72.4-4.2,82.7s-23.3-1.7-23.1-20.3.5-37-9-47.6-33.4-9.6-38.4-23.8c-1.7-4.7-3.3-11.4,0-15.9A11.9,11.9,0,0,1,88,145.2Z"/>
      </g>
      <g>
        <circle id="Elipse_382" data-name="Elipse 382" class="cls-18" cx="1503.8" cy="238.2" r="19.8"/>
        <g id="Grupo_5706" data-name="Grupo 5706">
          <path id="Caminho_9618" data-name="Caminho 9618" class="cls-19" d="M1496.2,246.6a1.1,1.1,0,0,0,.8.3c.7-.1.9-.8.9-1.5a7.1,7.1,0,0,0-1.6-3.9,14.7,14.7,0,0,1-2.4-3.7,3.5,3.5,0,0,1,.8-4c1.2-1,3-.6,4.4-1.3a.8.8,0,0,0,.4-.4c.3-.4,0-.9-.3-1.3a18.6,18.6,0,0,1-3.3-7.8,15.8,15.8,0,0,1,.1-3.1,20.7,20.7,0,0,0-9,7.6l.7.2c2.3,1.1,2.6,4,2.8,6.5a22.9,22.9,0,0,0,1.4,6.9A10.2,10.2,0,0,0,1496.2,246.6Z"/>
          <path id="Caminho_9619" data-name="Caminho 9619" class="cls-19" d="M1501.4,257.9a7.3,7.3,0,0,0-.8-3.8c-1.2-1.8-3.7-2.3-5.9-2H1493a2.7,2.7,0,0,1-.8-.7c-2.1-2.5-3.3-5.7-4.9-8.6-.8-1.2-1.9-2.6-3.3-2.6A19.9,19.9,0,0,0,1501.4,257.9Z"/>
          <path id="Caminho_9620" data-name="Caminho 9620" class="cls-19" d="M1503.7,218.4h-.4a4.7,4.7,0,0,0-.5,1,11.4,11.4,0,0,1-.9,2.6c-.3.4-.8.7-1.1,1.1a2.9,2.9,0,0,0,.2,3.4,10.6,10.6,0,0,0,3.1,2.1,6.5,6.5,0,0,1,3,2.2,3.8,3.8,0,0,1,.7,2.9,8.8,8.8,0,0,0-.6,2.7,2.7,2.7,0,0,0,2.6,2,5,5,0,0,0,3.3-1.2l1.5-.9c1.5-.5,3.1.4,4.3,1.5s2.1,2.4,3.5,3.2l.9.3a17.3,17.3,0,0,0,.3-3.2,19.7,19.7,0,0,0-19.9-19.7Z"/>
          <path id="Caminho_9621" data-name="Caminho 9621" class="cls-19" d="M1518.7,246.3a21.6,21.6,0,0,0-3.4,1.7,4.5,4.5,0,0,1-3.6.3c-1.3-.6-1.9-2.3-2.5-3.6a16.3,16.3,0,0,0-2.3-3.5c-.9-1.1-2.3-2.2-3.8-1.9s-2.6,1.9-3,3.4a4.1,4.1,0,0,0,.1,2.9c.8,1.7,3.2,2.2,4.1,3.9s0,3-.1,4.5a7,7,0,0,0,.9,4,19.9,19.9,0,0,0,16.7-11.6A4.7,4.7,0,0,0,1518.7,246.3Z"/>
        </g>
        <path id="Caminho_9624" data-name="Caminho 9624" class="cls-18" d="M1501.9,219.6a19.8,19.8,0,0,1,19.9,19.8,19.5,19.5,0,0,1-7.5,15.6,19.8,19.8,0,1,0-21.1-33.6l-1.7,1.2A19.1,19.1,0,0,1,1501.9,219.6Z"/>
      </g>
      <g>
        <circle id="Elipse_384" data-name="Elipse 384" class="cls-20" cx="1208.9" cy="139.3" r="58.1"/>
        <circle id="Elipse_385" data-name="Elipse 385" class="cls-21" cx="1205.5" cy="139.3" r="45.1"/>
        <g id="Grupo_5710" data-name="Grupo 5710">
          <path id="Caminho_9628" data-name="Caminho 9628" class="cls-22" d="M1230.7,134.1a3,3,0,0,0-1.2-1.7c-1.3-.6-2.8.3-3.6,1.5s-2.3,6.2-2.3,9.5.4,6.7-.6,9.9-3.5,6.2-6.8,6.3-6.4-2.9-9.8-3.5a1.7,1.7,0,0,0-1.4,0c-1,.4-1.2,1.8-1.3,2.9-.2,6.5-1.2,13.1-4.4,18.8a24.9,24.9,0,0,1-4.3,5.4,45.2,45.2,0,0,0,26.7-1.8,3.8,3.8,0,0,1-.9-1.3c-2.7-4.9.6-10.9,3.5-15.7a55.5,55.5,0,0,0,7-14.5A24.5,24.5,0,0,0,1230.7,134.1Z"/>
          <path id="Caminho_9629" data-name="Caminho 9629" class="cls-22" d="M1236.3,106.4c-1.8,2.5-3.4,5-3.5,8-.3,4.9,3.6,9.2,7.9,11.6a6.7,6.7,0,0,1,3.1,2.4,5.9,5.9,0,0,1,.5,2.3c.6,7.5-1.6,14.9-2.4,22.4-.3,3.2-.1,7.2,2.5,9.1A45.1,45.1,0,0,0,1236.3,106.4Z"/>
          <path id="Caminho_9630" data-name="Caminho 9630" class="cls-22" d="M1178.9,175.8l.6.4a8.2,8.2,0,0,0,2.5-1.2c1.7-1.2,3.1-3,5.1-3.5s2.3-.1,3.4-.4c2.8-.7,4.4-3.8,4.2-6.7s-1.5-5.4-2.8-8-2.7-5.2-2.6-8a8.4,8.4,0,0,1,2.8-6.3c1.5-1.4,3.6-2.3,4.6-4.1s.2-5.6-2-7.3-5-2.1-7.7-2.2a13.5,13.5,0,0,1-4-.3c-3.4-1-5.3-4.9-5.9-8.5s-.5-7.3-2.1-10.6a4.6,4.6,0,0,0-1.3-1.8,45.1,45.1,0,0,0-.3,63.8,41.3,41.3,0,0,0,5.5,4.7Z"/>
          <path id="Caminho_9631" data-name="Caminho 9631" class="cls-22" d="M1189.1,104.3c2.6,1,5.5,1,8.3,1.5s5.7,1.8,7.1,4.2.4,6.8-.2,10.2a29,29,0,0,0-.6,9.3c.3,3.4,1.4,7.2,4.5,8.8s7.1-.1,10-2.4a10,10,0,0,0,3.8-5.4c.7-4.3-3-8.4-2.3-12.6s3.9-5.5,6.2-8.2a17.4,17.4,0,0,0,3.8-8.5,45.4,45.4,0,0,0-46.4-1.2A12.3,12.3,0,0,0,1189.1,104.3Z"/>
        </g>
        <path id="Caminho_9632" data-name="Caminho 9632" class="cls-23" d="M1195.6,95.2a45.2,45.2,0,0,1-6.8,85.8,45.1,45.1,0,1,0,6.8-85.8Z"/>
        <path id="Caminho_9634" data-name="Caminho 9634" class="cls-24" d="M1183.8,175.9a45.1,45.1,0,0,1,25.1-81.6,45.1,45.1,0,0,0-6.9,90,40.5,40.5,0,0,0,4.9.1A44.9,44.9,0,0,1,1183.8,175.9Z"/>
        <g id="Grupo_5712" data-name="Grupo 5712">
          <path id="Caminho_9636" data-name="Caminho 9636" class="cls-25" d="M1174,139.2c3.2-.7,6.8,3.3,8,8.9s-.3,10.8-3.5,11.5-6.7-3.3-8-8.9S1170.8,139.9,1174,139.2Z"/>
          <path id="Caminho_9637" data-name="Caminho 9637" class="cls-25" d="M1174.9,123.7c1.9.3,3,3.2,2.5,6.6s-2.4,5.9-4.3,5.6-3-3.2-2.5-6.6S1173,123.4,1174.9,123.7Z"/>
        </g>
      </g>
      <g>
        <circle id="Elipse_387" data-name="Elipse 387" class="cls-26" cx="389.1" cy="330.3" r="15.7"/>
        <g id="Grupo_5714" data-name="Grupo 5714" class="cls-27">
          <path id="Caminho_9638" data-name="Caminho 9638" class="cls-28" d="M393,338.3a.8.8,0,0,0,.5-.5c.1-.5-.3-.9-.8-1.1a5,5,0,0,0-3.4.1,12.8,12.8,0,0,1-3.4.7,2.9,2.9,0,0,1-2.7-1.8c-.3-1.1.4-2.4.3-3.6a.6.6,0,0,0-.1-.4c-.2-.3-.7-.3-1.1-.2a13.8,13.8,0,0,1-6.7.2,8.8,8.8,0,0,1-2.2-1,15.5,15.5,0,0,0,3,8.8l.3-.4c1.5-1.4,3.7-.8,5.6-.2a20.5,20.5,0,0,0,5.5,1A8,8,0,0,0,393,338.3Z"/>
          <path id="Caminho_9639" data-name="Caminho 9639" class="cls-28" d="M402.8,337.7a5,5,0,0,0-3-.4c-1.6.3-2.7,2-3.1,3.7a2.7,2.7,0,0,1-.6,1.2l-.7.4c-2.4.9-5.1.8-7.7,1.2a3.3,3.3,0,0,0-2.8,1.6A15.4,15.4,0,0,0,402.8,337.7Z"/>
          <path id="Caminho_9640" data-name="Caminho 9640" class="cls-28" d="M374.5,324.6a.4.4,0,0,0-.1.3c.1.3.4.5.6.7s1.3.8,1.6,1.4a11.7,11.7,0,0,0,.5,1.1,2.2,2.2,0,0,0,2.6.9,8.1,8.1,0,0,0,2.4-1.7,6,6,0,0,1,2.5-1.6,2.7,2.7,0,0,1,2.3.4,7.1,7.1,0,0,0,1.8,1.2A2.2,2.2,0,0,0,391,326a4.5,4.5,0,0,0,0-2.8,6.8,6.8,0,0,1-.2-1.4,3.4,3.4,0,0,1,2.3-2.7,15.4,15.4,0,0,0,3.4-1.7,1,1,0,0,0,.5-.6,15.7,15.7,0,0,0-21.4,5.6,11.3,11.3,0,0,0-1.1,2.2Z"/>
          <path id="Caminho_9641" data-name="Caminho 9641" class="cls-28" d="M399.3,321.7a15.3,15.3,0,0,0,.3,3,3.9,3.9,0,0,1-.8,2.7,5,5,0,0,1-3.4.8,11.4,11.4,0,0,0-3.2.7,3.3,3.3,0,0,0-2.5,2.2,4.1,4.1,0,0,0,3.8,4c1.5-.1,2.5-1.8,4-1.9s2.2.8,3.3,1.3a5.9,5.9,0,0,0,3.2.6,15.9,15.9,0,0,0-3.7-15.7A4.3,4.3,0,0,0,399.3,321.7Z"/>
        </g>
        <path id="Caminho_9642" data-name="Caminho 9642" class="cls-29" d="M403,323.1a15.7,15.7,0,0,1-17.5,13.6,15.9,15.9,0,0,1-11.9-8.3,15.7,15.7,0,1,0,29.4-5.3Z"/>
        <path id="Caminho_9643" data-name="Caminho 9643" class="cls-30" d="M404.7,329.9c-.3,8-7.6,14.3-16.2,14s-14.2-5.8-15-13c-.3,8,6.4,14.8,15.1,15.1s15.9-6,16.2-14.1A14.1,14.1,0,0,0,404.7,329.9Z"/>
        <path id="Caminho_9644" data-name="Caminho 9644" class="cls-26" d="M374.9,326.3a15.5,15.5,0,0,1,20.2-8.9,15.7,15.7,0,0,1,9.4,10.1,15.7,15.7,0,1,0-30.8,5.7,9.4,9.4,0,0,0,.4,1.6A14.6,14.6,0,0,1,374.9,326.3Z"/>
        <path id="Caminho_9645" data-name="Caminho 9645" class="cls-31" d="M375.5,328.4c.2-1.9,1.9-4.2,2.8-5.4a14.8,14.8,0,0,1,7.9-5.5c8.4-2.3,14.5,4.1,15.6,7.1s-2.1,4.3-5.7,2.9-7-2.9-9.8-1.9-4.4,5.7-7.5,5.6a3.7,3.7,0,0,1-3.1-1.2A2.9,2.9,0,0,1,375.5,328.4Z"/>
        <g id="Grupo_5716" data-name="Grupo 5716" class="cls-32">
          <path id="Caminho_9646" data-name="Caminho 9646" class="cls-33" d="M386.3,319.8c.5,1-.5,2.5-2.3,3.4s-3.6.9-4.1-.1.5-2.6,2.3-3.5S385.8,318.8,386.3,319.8Z"/>
          <path id="Caminho_9647" data-name="Caminho 9647" class="cls-33" d="M391.6,318.7c.1.7-.8,1.3-2,1.4s-2.2-.3-2.3-.9.8-1.3,2-1.5S391.5,318,391.6,318.7Z"/>
        </g>
      </g>
      <g>
        <circle id="Elipse_388" data-name="Elipse 388" class="cls-34" cx="1742.7" cy="406" r="31.7"/>
        <circle id="Elipse_389" data-name="Elipse 389" class="cls-35" cx="1742.7" cy="406" r="26"/>
        <path id="Caminho_9648" data-name="Caminho 9648" class="cls-35" d="M1718,407.3a25.9,25.9,0,0,1,44.4-18.2,25.9,25.9,0,0,0-39.4,33.7l1.2,1.4A25.6,25.6,0,0,1,1718,407.3Z"/>
        <path id="Caminho_9649" data-name="Caminho 9649" class="cls-36" d="M1727.4,420a23.1,23.1,0,0,1,39-16.6,23,23,0,0,0-45.4,7.5,22.5,22.5,0,0,0,6.7,12.8A23.1,23.1,0,0,1,1727.4,420Z"/>
        <path id="Caminho_9650" data-name="Caminho 9650" class="cls-37" d="M1758.8,385.6a23.7,23.7,0,0,1-42.1,20.6,26,26,0,1,0,51.9-.5A25.6,25.6,0,0,0,1758.8,385.6Z"/>
        <path id="Caminho_9651" data-name="Caminho 9651" class="cls-38" d="M1764.4,394.9a24.5,24.5,0,0,1-44.8,19.3,24.5,24.5,0,1,0,46.2-16.2,17.3,17.3,0,0,0-1.4-3.1Z"/>
        <g id="Grupo_5719" data-name="Grupo 5719">
          <path id="Caminho_9652" data-name="Caminho 9652" class="cls-39" d="M1759.7,393.9c-1.1,2.7-5.1,3.8-9,2.2s-6-5-4.9-7.7,5.1-3.8,8.9-2.3S1760.8,391.1,1759.7,393.9Zm-16.2-.3c-1.7-.7-3.6-.2-4.1,1.1s.5,2.8,2.3,3.5,3.6.3,4.1-1S1745.3,394.3,1743.5,393.6Z"/>
          <g id="Grupo_5718" data-name="Grupo 5718">
            <path id="Caminho_9653" data-name="Caminho 9653" class="cls-39" d="M1743.5,393.6c-1.7-.7-3.6-.2-4.1,1.1a4.3,4.3,0,0,0-.1.5,3.6,3.6,0,0,1,3.7-.4,3.4,3.4,0,0,1,2.4,3l.4-.6C1746.3,395.9,1745.3,394.3,1743.5,393.6Z"/>
            <path id="Caminho_9654" data-name="Caminho 9654" class="cls-39" d="M1754.7,386.1c-3.8-1.5-7.8-.5-8.9,2.3-.1.1-.1.3-.2.5,1.5-2.2,5.1-2.9,8.6-1.6s5.8,4.6,5.1,7.3l.4-.7C1760.8,391.1,1758.5,387.7,1754.7,386.1Z"/>
          </g>
        </g>
        <g id="Grupo_5720" data-name="Grupo 5720">
          <path id="Caminho_9655" data-name="Caminho 9655" class="cls-39" d="M1751.2,422.3c-.2,2.3-2.9,4-6.1,3.8s-5.7-2.3-5.5-4.6,2.9-4,6.1-3.8S1751.4,420,1751.2,422.3Z"/>
          <path id="Caminho_9656" data-name="Caminho 9656" class="cls-39" d="M1750.7,420.4v.4c-.1,2.3-2.9,4-6.1,3.7a6,6,0,0,1-5-2.7c.1,2.2,2.4,4.1,5.5,4.3s5.9-1.5,6.1-3.8A3.2,3.2,0,0,0,1750.7,420.4Z"/>
        </g>
        <g id="Grupo_5722" data-name="Grupo 5722">
          <path id="Caminho_9657" data-name="Caminho 9657" class="cls-39" d="M1765.1,404.6c1.7.6,1.8,4.3.3,8.1s-4.1,6.4-5.8,5.8-1.9-4.4-.4-8.2S1763.4,403.9,1765.1,404.6Zm-7,17.2c-1.2,1-1.7,2.2-1.3,2.7s1.7.1,2.9-.9,1.7-2.2,1.2-2.7S1759.2,420.8,1758.1,421.8Zm4.5-22.3c.4,2.1,1.4,3.6,2.3,3.4s1.4-2,1-4-1.5-3.6-2.4-3.4-1.3,2-.9,4Zm-3.1,3.9c-.6,1.3-.6,2.7,0,3s1.6-.6,2.2-2,.7-2.7,0-3-1.6.6-2.2,2Z"/>
          <g id="Grupo_5721" data-name="Grupo 5721">
            <path id="Caminho_9658" data-name="Caminho 9658" class="cls-39" d="M1764.8,398.7c.4,1.8.1,3.5-.7,3.9a1.1,1.1,0,0,0,.8.3c.9-.2,1.4-2,1-4s-1.5-3.6-2.4-3.4h-.2A4.6,4.6,0,0,1,1764.8,398.7Z"/>
            <path id="Caminho_9659" data-name="Caminho 9659" class="cls-39" d="M1760.6,404.2a4.2,4.2,0,0,1-1.4,1.8c0,.2.1.3.3.4s1.6-.6,2.2-2,.6-2.7,0-3a.9.9,0,0,0-.7.1A3.6,3.6,0,0,1,1760.6,404.2Z"/>
            <path id="Caminho_9660" data-name="Caminho 9660" class="cls-39" d="M1759.9,420.8c.3.5-.2,1.6-1.3,2.6a4,4,0,0,1-1.8,1h0c.5.5,1.7.1,2.9-.9s1.7-2.2,1.2-2.7A.9.9,0,0,0,1759.9,420.8Z"/>
            <path id="Caminho_9661" data-name="Caminho 9661" class="cls-39" d="M1765.1,404.6a1.1,1.1,0,0,0-.8-.1c1.4.9,1.5,4.4.1,8s-3.4,5.6-5.1,5.8h.3c1.7.7,4.3-1.8,5.8-5.7S1766.8,405.2,1765.1,404.6Z"/>
          </g>
        </g>
        <g id="Grupo_5724" data-name="Grupo 5724">
          <path id="Caminho_9662" data-name="Caminho 9662" class="cls-39" d="M1733.2,385.5c1.4,1.2.4,4.7-2.2,7.9s-5.8,4.9-7.3,3.7-.4-4.7,2.2-7.9S1731.8,384.4,1733.2,385.5Zm3.7-2.2c-1.4.6-2.3,1.6-2,2.2s1.6.6,3,0,2.2-1.5,2-2.1-1.6-.7-3-.1Zm-17,15.8c-.7,1.3-.9,2.6-.3,2.9s1.7-.5,2.4-1.8.9-2.6.3-2.9-1.7.4-2.4,1.8Z"/>
          <g id="Grupo_5723" data-name="Grupo 5723">
            <path id="Caminho_9663" data-name="Caminho 9663" class="cls-39" d="M1720.5,400.2c.7-1.2,1.5-1.9,2.2-1.9s-.1-.9-.4-1-1.7.4-2.4,1.8-.9,2.6-.3,2.9h.3A6,6,0,0,1,1720.5,400.2Z"/>
            <path id="Caminho_9664" data-name="Caminho 9664" class="cls-39" d="M1726.5,390.3c2.6-3.1,5.8-4.8,7.3-3.7a2.4,2.4,0,0,0-.6-1.1c-1.4-1.1-4.7.5-7.3,3.7s-3.6,6.7-2.2,7.9h0C1723.4,395.5,1724.5,392.8,1726.5,390.3Z"/>
            <path id="Caminho_9665" data-name="Caminho 9665" class="cls-39" d="M1737.5,384.4a5.9,5.9,0,0,1,2.3-.4.7.7,0,0,0,.1-.6c-.3-.7-1.6-.7-3-.1s-2.3,1.6-2,2.2a.7.7,0,0,0,.7.4A4,4,0,0,1,1737.5,384.4Z"/>
          </g>
        </g>
        <g id="Grupo_5726" data-name="Grupo 5726">
          <path id="Caminho_9666" data-name="Caminho 9666" class="cls-39" d="M1730.4,424.4c-1,1.1-3.6.2-5.9-1.9s-3.3-4.7-2.3-5.8,3.6-.2,5.9,1.9S1731.4,423.4,1730.4,424.4Zm5.1.9c-1.4-.8-2.8-.9-3.1-.3s.4,1.8,1.7,2.6,2.7.9,3.1.3-.4-1.8-1.7-2.6ZM1724.6,412c-.9-1.3-2.2-1.9-2.7-1.4s-.3,1.8.7,3,2.2,1.8,2.8,1.4.2-1.8-.8-3Z"/>
          <g id="Grupo_5725" data-name="Grupo 5725">
            <path id="Caminho_9667" data-name="Caminho 9667" class="cls-39" d="M1725.4,415a1.4,1.4,0,0,0,.2-.7,3.6,3.6,0,0,1-2.3-1.5,4.3,4.3,0,0,1-1-2.4.5.5,0,0,0-.4.2c-.6.4-.3,1.8.7,3S1724.8,415.4,1725.4,415Z"/>
            <path id="Caminho_9668" data-name="Caminho 9668" class="cls-39" d="M1734.8,426.7a3.2,3.2,0,0,1-1.8-2.1c-.2.1-.5.2-.6.4s.4,1.8,1.7,2.6,2.7.9,3.1.3a.9.9,0,0,0,.1-.5A3.7,3.7,0,0,1,1734.8,426.7Z"/>
            <path id="Caminho_9669" data-name="Caminho 9669" class="cls-39" d="M1725.2,421.7c-2-1.9-3-4-2.6-5.3l-.4.3c-1,1.1,0,3.7,2.3,5.8s4.9,3,5.9,1.9a.5.5,0,0,0,.3-.5C1729.5,424.4,1727.2,423.6,1725.2,421.7Z"/>
          </g>
        </g>
        <g id="Grupo_5731" data-name="Grupo 5731" class="cls-40">
          <g id="Grupo_5730" data-name="Grupo 5730">
            <g id="Grupo_5728" data-name="Grupo 5728" class="cls-40">
              <path id="Caminho_9670" data-name="Caminho 9670" class="cls-41" d="M1733.1,391.1c1.3,1.2.7,4.1-1.4,6.5s-4.9,3.4-6.3,2.2-.7-4.1,1.4-6.5S1731.7,389.9,1733.1,391.1Z"/>
            </g>
            <g id="Grupo_5729" data-name="Grupo 5729" class="cls-40">
              <path id="Caminho_9671" data-name="Caminho 9671" class="cls-41" d="M1740.3,386.5c.4.9-.5,2.4-2.2,3.3s-3.4.8-3.9-.1.5-2.5,2.1-3.3S1739.8,385.5,1740.3,386.5Z"/>
            </g>
          </g>
        </g>
        <path id="Caminho_9672" data-name="Caminho 9672" class="cls-42" d="M1787.3,382.5c-2.3-4.3-14-3-29.3,2.6a12.1,12.1,0,0,1,2.4,2.1c9.8-3,16.9-3.3,18.5-.3,2.6,5-11.9,17.8-32.4,28.6s-39.4,15.2-42,10.2c-1.7-3.3,3.5-9.6,12.8-16.5a15.7,15.7,0,0,1-.5-3c-14.4,9.9-23.1,19.3-20.7,23.8,3.2,6.3,26.3.7,51.4-12.5S1790.5,388.7,1787.3,382.5Z"/>
        <path id="Caminho_9673" data-name="Caminho 9673" class="cls-43" d="M1762.5,384.5a1.4,1.4,0,0,1,0-.9,39.6,39.6,0,0,0-4.5,1.5,12.1,12.1,0,0,1,2.4,2.1c9.8-3,16.9-3.3,18.5-.3,2.6,5-11.9,17.8-32.4,28.6s-39.4,15.2-42,10.2c-1.7-3.3,3.5-9.6,12.8-16.5a15.7,15.7,0,0,1-.5-3c-7.9,5.5-14.1,10.8-17.7,15.2,4.3-4,11.7-10.7,12-9.3s-16.7,11.9-5.8,16.3,39.2-10.9,50.2-17,24.1-16.8,26.9-24.3S1763.2,387.9,1762.5,384.5Z"/>
      </g>
      <g>
        <circle id="Elipse_390" data-name="Elipse 390" class="cls-44" cx="746.6" cy="107.8" r="26"/>
        <path id="Caminho_9676" data-name="Caminho 9676" class="cls-45" d="M727.4,92.1a26,26,0,0,1,45.1,16.6,26,26,0,1,0-51.9-1.7v1.8A26,26,0,0,1,727.4,92.1Z"/>
        <path id="Caminho_9677" data-name="Caminho 9677" class="cls-45" d="M725.8,107.8a23,23,0,0,1,32.5-1.4,22.6,22.6,0,0,1,7.4,15.6,23,23,0,1,0-38.5-25.2,23.3,23.3,0,0,0-3.7,14A30,30,0,0,1,725.8,107.8Z"/>
        <path id="Caminho_9679" data-name="Caminho 9679" class="cls-46" d="M770.1,114.3a24.5,24.5,0,0,1-46.2-16,24.5,24.5,0,1,0,45,19.3,21.9,21.9,0,0,0,1.2-3.3Z"/>
        <g id="Grupo_5740" data-name="Grupo 5740" class="cls-47">
          <path id="Caminho_9680" data-name="Caminho 9680" class="cls-46" d="M767.2,110.4c-2.6,1.3-6.3-.7-8.1-4.4s-1.1-7.8,1.6-9.1,6.3.7,8.1,4.4S769.9,109.1,767.2,110.4ZM755.5,99.3c-.8-1.7-2.5-2.6-3.7-2s-1.6,2.4-.7,4.1,2.5,2.7,3.7,2.1,1.5-2.5.7-4.2Z"/>
          <g id="Grupo_5739" data-name="Grupo 5739">
            <path id="Caminho_9681" data-name="Caminho 9681" class="cls-46" d="M755.5,99.3c-.8-1.7-2.5-2.6-3.7-2l-.5.3a3.6,3.6,0,0,1,3,2.2,3.5,3.5,0,0,1-.2,3.8h.7C756,102.9,756.3,101,755.5,99.3Z"/>
            <path id="Caminho_9682" data-name="Caminho 9682" class="cls-46" d="M768.8,101.3c-1.8-3.7-5.4-5.7-8.1-4.4l-.5.3c2.6-.6,5.8,1.3,7.4,4.6s1.2,7.3-1.1,8.9l.7-.3C769.9,109.1,770.6,105,768.8,101.3Z"/>
          </g>
        </g>
        <g id="Grupo_5741" data-name="Grupo 5741" class="cls-47">
          <ellipse id="Elipse_391" data-name="Elipse 391" class="cls-46" cx="737.8" cy="121.4" rx="4.2" ry="5.8" transform="translate(118.8 540.8) rotate(-43.5)"/>
          <path id="Caminho_9683" data-name="Caminho 9683" class="cls-46" d="M742.7,123.9l-.2.3c-1.7,1.6-4.9,1-7.1-1.4a6.1,6.1,0,0,1-1.9-5.4c-1.4,1.7-.9,4.7,1.2,6.9s5.4,2.9,7.1,1.3A2.6,2.6,0,0,0,742.7,123.9Z"/>
        </g>
        <g id="Grupo_5743" data-name="Grupo 5743" class="cls-47">
          <path id="Caminho_9684" data-name="Caminho 9684" class="cls-46" d="M764,121.9c.8,1.7-1.6,4.5-5.3,6.3s-7.3,1.9-8.1.2,1.5-4.4,5.2-6.2S763.2,120.3,764,121.9Zm-16.8,8c-1.5,0-2.7.5-2.8,1.1s1.2,1.3,2.7,1.3,2.8-.5,2.8-1.1-1.2-1.3-2.7-1.3Zm18.4-13.4c-1.1,1.8-1.4,3.6-.6,4.1s2.3-.6,3.4-2.3,1.4-3.6.6-4.1-2.3.5-3.4,2.3Zm-4.9.8c-1.4.5-2.3,1.5-2,2.1s1.5.7,2.9.1,2.3-1.6,2.1-2.2-1.6-.6-3,0Z"/>
          <g id="Grupo_5742" data-name="Grupo 5742">
            <path id="Caminho_9685" data-name="Caminho 9685" class="cls-46" d="M767.8,117.4c-1,1.6-2.4,2.6-3.2,2.4a1.2,1.2,0,0,0,.4.8c.8.5,2.3-.6,3.4-2.3s1.4-3.6.6-4.1h-.2A5.6,5.6,0,0,1,767.8,117.4Z"/>
            <path id="Caminho_9686" data-name="Caminho 9686" class="cls-46" d="M761,118.6a3.8,3.8,0,0,1-2.4.4v.4c.3.7,1.6.7,3,.1s2.3-1.5,2.1-2.2a1,1,0,0,0-.7-.4A3.4,3.4,0,0,1,761,118.6Z"/>
            <path id="Caminho_9687" data-name="Caminho 9687" class="cls-46" d="M749.2,130.4c-.1.6-1.2,1.1-2.7,1a4.5,4.5,0,0,1-2-.4h-.1c0,.7,1.2,1.3,2.7,1.3s2.8-.5,2.8-1.1S749.6,130.6,749.2,130.4Z"/>
            <path id="Caminho_9688" data-name="Caminho 9688" class="cls-46" d="M764,121.9l-.5-.6c.4,1.7-1.9,4.3-5.4,6s-6.3,1.8-7.6.9,0,.2.1.2c.8,1.7,4.4,1.6,8.1-.2S764.8,123.6,764,121.9Z"/>
          </g>
        </g>
        <g id="Grupo_5747" data-name="Grupo 5747" class="cls-47">
          <path id="Caminho_9693" data-name="Caminho 9693" class="cls-46" d="M725,113.1c-1.4.2-2.8-2.3-3-5.4s.7-5.7,2.2-5.8,2.8,2.3,3,5.4S726.5,113,725,113.1Zm3.1,4.1c-.4-1.5-1.3-2.5-2-2.3s-.9,1.6-.5,3.1,1.4,2.5,2.1,2.3.9-1.6.4-3.1Zm1.1-17.1c.1-1.6-.4-2.9-1.1-2.9s-1.4,1.1-1.5,2.7.3,2.8,1.1,2.9,1.4-1.2,1.5-2.7Z"/>
          <g id="Grupo_5746" data-name="Grupo 5746">
            <path id="Caminho_9694" data-name="Caminho 9694" class="cls-46" d="M727.7,102.8a.8.8,0,0,0,.6-.4,3.6,3.6,0,0,1-.7-2.7,3.8,3.8,0,0,1,.9-2.4h-.4c-.8-.1-1.4,1.1-1.5,2.7S726.9,102.7,727.7,102.8Z"/>
            <path id="Caminho_9695" data-name="Caminho 9695" class="cls-46" d="M726.7,117.8a3.4,3.4,0,0,1,.1-2.8.9.9,0,0,0-.7-.1c-.7.2-.9,1.6-.5,3s1.4,2.6,2.1,2.4l.4-.3A4,4,0,0,1,726.7,117.8Z"/>
            <path id="Caminho_9696" data-name="Caminho 9696" class="cls-46" d="M723,107.6c-.2-2.7.5-5,1.7-5.6h-.5c-1.5.1-2.5,2.7-2.2,5.8s1.6,5.6,3,5.4a.7.7,0,0,0,.6-.2C724.3,112.6,723.2,110.3,723,107.6Z"/>
          </g>
        </g>
        <g id="Grupo_5752" data-name="Grupo 5752" class="cls-47">
          <g id="Grupo_5751" data-name="Grupo 5751">
            <g id="Grupo_5749" data-name="Grupo 5749" class="cls-40">
              <path id="Caminho_9697" data-name="Caminho 9697" class="cls-48" d="M749.5,90.4c.2,1.8-2.3,3.5-5.4,3.8s-5.9-.8-6.1-2.6,2.2-3.5,5.4-3.9S749.3,88.6,749.5,90.4Z"/>
            </g>
            <g id="Grupo_5750" data-name="Grupo 5750" class="cls-40">
              <path id="Caminho_9698" data-name="Caminho 9698" class="cls-48" d="M757.9,91.8c-.3,1-1.9,1.5-3.8,1s-3-1.7-2.8-2.7,2-1.5,3.8-1S758.2,90.8,757.9,91.8Z"/>
            </g>
          </g>
        </g>
        <path id="Caminho_9699" data-name="Caminho 9699" class="cls-49" d="M795.3,120.6c1.2-4.7-8.3-11.6-23.4-17.8a11.2,11.2,0,0,1,.4,3.2c9.2,4.4,14.7,8.9,13.8,12.2-1.5,5.5-20.8,5.1-43.2-.8s-39.3-15.3-37.8-20.9c.9-3.5,9-4.6,20.5-3.4a11,11,0,0,1,1.7-2.5c-17.3-2.5-30.1-1.4-31.4,3.5-1.8,6.8,19,18.2,46.4,25.5S793.5,127.4,795.3,120.6Z"/>
        <path id="Caminho_9700" data-name="Caminho 9700" class="cls-50" d="M775.6,105.3c.2-.2.4-.5.7-.6l-4.3-1.9a16.6,16.6,0,0,1,.3,3.2c9.2,4.4,14.7,8.9,13.8,12.2-1.5,5.5-20.8,5.1-43.2-.8s-39.3-15.3-37.8-20.9c.9-3.5,9-4.6,20.5-3.4a11,11,0,0,1,1.7-2.5c-9.6-1.4-17.7-1.7-23.3-.8,5.8-.1,15.8,0,15.1,1.2s-20.4-2.5-15.3,8.1,36.3,18.5,48.5,21.4,29.1,3.9,36.2.3S773.9,108.4,775.6,105.3Z"/>
      </g>
      <g>
        <circle id="Elipse_393" data-name="Elipse 393" class="cls-51" cx="407.1" cy="128.4" r="32.1"/>
        <path id="Caminho_9703" data-name="Caminho 9703" class="cls-52" d="M376.6,131.8a32.1,32.1,0,0,1,53.7-25.6A32.1,32.1,0,1,0,384,150.7l1.6,1.5A31.9,31.9,0,0,1,376.6,131.8Z"/>
        <path id="Caminho_9704" data-name="Caminho 9704" class="cls-53" d="M389.2,146.8a28.4,28.4,0,0,1,26.8-30,28.1,28.1,0,0,1,20.2,6.8,28.5,28.5,0,1,0-46.4,27.8A34,34,0,0,1,389.2,146.8Z"/>
        <path id="Caminho_9706" data-name="Caminho 9706" class="cls-54" d="M433.2,113.2a30.3,30.3,0,0,1-54,27.1A30.3,30.3,0,1,0,435.1,117a26.9,26.9,0,0,0-1.9-3.8Z"/>
        <g id="Grupo_5761" data-name="Grupo 5761">
          <path id="Caminho_9707" data-name="Caminho 9707" class="cls-55" d="M427.2,112.3c-1.1,3.5-6,5-10.8,3.4s-7.9-5.7-6.7-9.2,6-5,10.9-3.4S428.4,108.8,427.2,112.3Zm-19.9.8c-2.3-.7-4.5,0-5,1.6s.8,3.5,3,4.2,4.5.1,5-1.5-.8-3.5-3-4.3Z"/>
          <g id="Grupo_5760" data-name="Grupo 5760">
            <path id="Caminho_9708" data-name="Caminho 9708" class="cls-56" d="M407.3,113.1c-2.3-.7-4.5,0-5,1.6a1.7,1.7,0,0,0-.1.7,4.4,4.4,0,0,1,4.5-.8c1.9.7,3.2,2.1,3.2,3.5l.5-.7C410.9,115.8,409.5,113.9,407.3,113.1Z"/>
            <path id="Caminho_9709" data-name="Caminho 9709" class="cls-57" d="M420.6,103.1c-4.9-1.6-9.8-.1-10.9,3.4-.1.2-.1.4-.2.6,1.7-2.8,6.1-4,10.5-2.5s7.5,5.3,6.8,8.6a3.6,3.6,0,0,0,.4-.9C428.4,108.8,425.4,104.7,420.6,103.1Z"/>
          </g>
        </g>
        <path id="Caminho_9727" data-name="Caminho 9727" class="cls-58" d="M458.9,96.3c-3.1-5.2-17.4-2.7-36,5.3A15,15,0,0,1,426,104c11.9-4.3,20.7-5.3,22.8-1.7,3.6,6.1-13.4,22.9-38,37.6s-47.5,21.7-51.2,15.6c-2.3-3.9,3.7-12.1,14.7-21.3a15.5,15.5,0,0,1-.8-3.6c-17.2,13.2-27.2,25.5-24,30.9,4.5,7.4,32.6-1.1,62.8-19.1S463.3,103.7,458.9,96.3Z"/>
        <path id="Caminho_9728" data-name="Caminho 9728" class="cls-54" d="M430,100.5a2,2,0,0,1,0-1.1c-1.8.6-3.6,1.4-5.4,2.2a23.4,23.4,0,0,1,3.1,2.4c11.8-4.4,20.6-5.3,22.8-1.7,3.6,6.1-13.4,22.9-38.1,37.6s-47.5,21.7-51.1,15.6c-2.3-3.9,3.7-12.1,14.7-21.3a15.5,15.5,0,0,1-.8-3.6c-9.5,7.3-16.8,14.3-20.9,20,5-5.3,13.8-14,14.1-12.4s-19.7,16-5.9,20.6,47.7-16.2,60.8-24.5,28.6-22.5,31.6-31.9S431.2,104.7,430,100.5Z"/>
      </g>
      <g>
        <circle id="Elipse_395" data-name="Elipse 395" class="cls-59" cx="1801.2" cy="178" r="23.8"/>
        <path id="Caminho_9734" data-name="Caminho 9734" class="cls-60" d="M1823.3,187.1c-6.2,11-20.5,15-31.8,9a22.4,22.4,0,0,1-10.7-27.9c-6.2,10.9-2.1,24.7,9.3,30.8s25.6,2,31.8-9A15.5,15.5,0,0,0,1823.3,187.1Z"/>
        <g id="Grupo_5782" data-name="Grupo 5782">
          <path id="Caminho_9735" data-name="Caminho 9735" class="cls-60" d="M1819.8,182.2c-2.5,1-5.6-1.1-6.9-4.7s-.3-7.2,2.2-8.1,5.7,1.2,7,4.7S1822.4,181.3,1819.8,182.2Zm-9.6-11.1c-.6-1.7-2.1-2.6-3.3-2.2s-1.6,2.1-1,3.7,2.1,2.6,3.2,2.2,1.7-2.1,1.1-3.7Z"/>
          <g id="Grupo_5781" data-name="Grupo 5781">
            <path id="Caminho_9736" data-name="Caminho 9736" class="cls-60" d="M1810.2,171.1c-.6-1.7-2.1-2.6-3.3-2.2l-.4.3a3.1,3.1,0,0,1,2.5,2.2,3.2,3.2,0,0,1-.5,3.5h.6C1810.3,174.4,1810.8,172.7,1810.2,171.1Z"/>
            <path id="Caminho_9737" data-name="Caminho 9737" class="cls-60" d="M1822.1,174.1c-1.3-3.5-4.4-5.6-7-4.7l-.4.2c2.4-.4,5.1,1.7,6.3,4.9s.4,6.7-1.9,7.9l.7-.2C1822.4,181.3,1823.4,177.7,1822.1,174.1Z"/>
          </g>
        </g>
        <g id="Grupo_5783" data-name="Grupo 5783" class="cls-47">
          <path id="Caminho_9738" data-name="Caminho 9738" class="cls-60" d="M1795.3,193.8c-1.7,1.3-4.5.5-6.3-1.8s-1.9-5.3-.3-6.6,4.5-.5,6.4,1.8S1797,192.5,1795.3,193.8Z"/>
          <path id="Caminho_9739" data-name="Caminho 9739" class="cls-60" d="M1796.3,192.3l-.2.3c-1.7,1.3-4.6.4-6.4-1.9a5.9,5.9,0,0,1-1.2-5.1c-1.4,1.4-1.2,4.1.5,6.4s4.6,3.1,6.3,1.8A3.1,3.1,0,0,0,1796.3,192.3Z"/>
        </g>
        <g id="Grupo_5785" data-name="Grupo 5785" class="cls-47">
          <path id="Caminho_9740" data-name="Caminho 9740" class="cls-60" d="M1815.9,192.5c.6,1.5-1.9,3.9-5.4,5.2s-6.9,1-7.5-.5,1.9-3.9,5.4-5.2S1815.3,190.9,1815.9,192.5Zm-16.1,5.7c-1.3-.2-2.5.2-2.6.8s1,1.2,2.4,1.4,2.5-.2,2.6-.8S1801.2,198.4,1799.8,198.2Zm18-10.5c-1.2,1.5-1.5,3.1-.9,3.6s2.2-.3,3.3-1.8,1.6-3.1.9-3.7-2.1.3-3.3,1.9Zm-4.5.2c-1.3.4-2.3,1.2-2.1,1.8s1.4.7,2.7.3,2.3-1.2,2.1-1.8-1.4-.7-2.7-.3Z"/>
          <g id="Grupo_5784" data-name="Grupo 5784">
            <path id="Caminho_9741" data-name="Caminho 9741" class="cls-60" d="M1819.7,188.7c-1,1.4-2.4,2.1-3.1,1.9a1.1,1.1,0,0,0,.3.7c.7.5,2.2-.3,3.3-1.8s1.6-3.1.9-3.7h-.2A4.9,4.9,0,0,1,1819.7,188.7Z"/>
            <path id="Caminho_9742" data-name="Caminho 9742" class="cls-60" d="M1813.4,189.2a3.8,3.8,0,0,1-2.1.1.6.6,0,0,0-.1.4c.2.6,1.4.7,2.7.3s2.3-1.2,2.1-1.8a1,1,0,0,0-.5-.4A3.4,3.4,0,0,1,1813.4,189.2Z"/>
            <path id="Caminho_9743" data-name="Caminho 9743" class="cls-60" d="M1801.7,198.8c-.2.6-1.3.9-2.6.7a3.3,3.3,0,0,1-1.9-.6h0c0,.6,1,1.2,2.4,1.4s2.5-.2,2.6-.8S1802,199.1,1801.7,198.8Z"/>
            <path id="Caminho_9744" data-name="Caminho 9744" class="cls-60" d="M1815.9,192.5a1,1,0,0,0-.5-.6c.3,1.5-2,3.7-5.4,4.9s-5.9,1.1-7,.1v.3c.6,1.5,4,1.8,7.5.5S1816.5,194,1815.9,192.5Z"/>
          </g>
        </g>
        <g id="Grupo_5787" data-name="Grupo 5787">
          <path id="Caminho_9745" data-name="Caminho 9745" class="cls-60" d="M1809.4,159.1c0,1.6-3,3.1-6.7,3.3s-6.9-1-7-2.7,2.9-3.2,6.7-3.4S1809.3,157.4,1809.4,159.1Zm3.7,1.2c-1.2-.6-2.4-.7-2.7-.1s.6,1.5,1.8,2.1,2.5.6,2.7,0-.5-1.5-1.8-2Zm-21.1-2c-1.3.3-2.3,1-2.2,1.6s1.4.8,2.7.5,2.3-1,2.2-1.6-1.3-.8-2.7-.5Z"/>
          <g id="Grupo_5786" data-name="Grupo 5786">
            <path id="Caminho_9746" data-name="Caminho 9746" class="cls-60" d="M1791.6,159.4a3,3,0,0,1,2.6.3c.4-.3.6-.6.5-.9s-1.3-.8-2.7-.5-2.3,1-2.2,1.6l.2.2A4.6,4.6,0,0,1,1791.6,159.4Z"/>
            <path id="Caminho_9747" data-name="Caminho 9747" class="cls-60" d="M1802,157.4c3.8-.1,6.9,1.1,7,2.7a1.8,1.8,0,0,0,.4-1c-.1-1.7-3.2-2.9-7-2.8s-6.8,1.7-6.7,3.4h0C1796.6,158.5,1799.1,157.6,1802,157.4Z"/>
            <path id="Caminho_9748" data-name="Caminho 9748" class="cls-60" d="M1812.8,161.4a3.3,3.3,0,0,1,1.6,1.3,1,1,0,0,0,.5-.4c.3-.5-.5-1.5-1.8-2s-2.4-.7-2.7-.1a.8.8,0,0,0,.2.8A3.5,3.5,0,0,1,1812.8,161.4Z"/>
          </g>
        </g>
        <g id="Grupo_5789" data-name="Grupo 5789">
          <path id="Caminho_9749" data-name="Caminho 9749" class="cls-60" d="M1781.2,180.9c-1.4,0-2.4-2.3-2.3-5.2s1.1-5.1,2.5-5.1,2.3,2.4,2.3,5.2S1782.5,181,1781.2,180.9Zm2.4,4c-.2-1.4-1-2.4-1.6-2.3s-1,1.4-.7,2.8,1,2.4,1.6,2.3,1-1.4.7-2.8Zm2.5-15.5c.3-1.4-.1-2.6-.7-2.7s-1.4.9-1.7,2.3.1,2.6.8,2.7,1.4-.9,1.6-2.3Z"/>
          <g id="Grupo_5788" data-name="Grupo 5788">
            <path id="Caminho_9750" data-name="Caminho 9750" class="cls-60" d="M1784.5,171.7c.2,0,.5,0,.6-.2a3.2,3.2,0,0,1-.4-2.5,3.8,3.8,0,0,1,1-2.1l-.3-.3c-.7-.1-1.4,1-1.6,2.4S1783.8,171.6,1784.5,171.7Z"/>
            <path id="Caminho_9751" data-name="Caminho 9751" class="cls-60" d="M1782.3,185.4a3,3,0,0,1,.3-2.6l-.6-.2c-.7.1-1,1.4-.7,2.8s1,2.4,1.6,2.3.3-.1.4-.3A3.2,3.2,0,0,1,1782.3,185.4Z"/>
            <path id="Caminho_9752" data-name="Caminho 9752" class="cls-60" d="M1779.8,175.7c.1-2.4.9-4.5,2-5h-.4c-1.4,0-2.5,2.3-2.5,5.1s.9,5.2,2.3,5.2h.5C1780.6,180.3,1779.8,178.2,1779.8,175.7Z"/>
          </g>
        </g>
        <g id="Grupo_5794" data-name="Grupo 5794" class="cls-40">
          <g id="Grupo_5793" data-name="Grupo 5793">
            <g id="Grupo_5791" data-name="Grupo 5791" class="cls-40">
              <path id="Caminho_9753" data-name="Caminho 9753" class="cls-61" d="M1805.5,162.4c0,1.6-2.4,3-5.3,3s-5.3-1.3-5.3-2.9,2.3-3,5.3-3.1S1805.5,160.7,1805.5,162.4Z"/>
            </g>
            <g id="Grupo_5792" data-name="Grupo 5792" class="cls-40">
              <path id="Caminho_9754" data-name="Caminho 9754" class="cls-61" d="M1813,164.5c-.3.9-1.9,1.1-3.5.5s-2.7-1.8-2.3-2.7,1.9-1.2,3.5-.6S1813.4,163.5,1813,164.5Z"/>
            </g>
          </g>
        </g>
        <path id="Caminho_9755" data-name="Caminho 9755" class="cls-62" d="M1844.5,194.1c1.5-4.2-6.5-11.3-19.7-18.4a14,14,0,0,1,0,2.9c8,4.9,12.6,9.5,11.5,12.4-1.8,4.9-19.4,2.9-39.2-4.6s-34.5-17.5-32.7-22.4c1.2-3.1,8.7-3.4,19.1-1.3a12,12,0,0,1,1.7-2.1c-15.5-3.9-27.2-4-28.9.4-2.2,6,15.7,18.3,40,27.4S1842.2,200.1,1844.5,194.1Z"/>
        <path id="Caminho_9756" data-name="Caminho 9756" class="cls-63" d="M1828,178.4a1.7,1.7,0,0,1,.6-.6l-3.8-2.1a14,14,0,0,1,0,2.9c8,4.9,12.6,9.5,11.5,12.4-1.8,4.9-19.4,2.9-39.2-4.6s-34.5-17.5-32.6-22.4c1.1-3.1,8.6-3.4,19-1.3a12,12,0,0,1,1.7-2.1c-8.5-2.1-15.9-3.1-21.1-2.9,5.3.5,14.4,1.5,13.7,2.5s-18.4-4-14.7,6,31.4,20.1,42.2,23.9,26.2,6.2,33,3.5S1826.1,181,1828,178.4Z"/>
      </g>
    </g>
  </g>
</svg>
