import React from 'react';
import { Box, Flex, Text, VStack } from '@chakra-ui/react';

import { FinanceiroMovimentacaoFinanceiraIcon } from 'icons/SemUso';

const FluxoCaixaListar: React.FC = () => {
  return (
    <Box p={6}>
      <Flex align="center" mb={6}>
        <FinanceiroMovimentacaoFinanceiraIcon size={24} />
        <Text fontSize="2xl" fontWeight="bold" ml={3}>
          Fluxo de Caixa
        </Text>
      </Flex>

      <VStack spacing={4} align="stretch">
        <Box
          p={6}
          bg="gray.50"
          borderRadius="md"
          border="1px"
          borderColor="gray.200"
        >
          <Text fontSize="lg" fontWeight="semibold" mb={2}>
            Bem-vindo ao Fluxo de Caixa
          </Text>
          <Text color="gray.600">
            Esta funcionalidade está em desenvolvimento. Em breve você poderá
            visualizar e gerenciar o fluxo de caixa da sua empresa de forma
            completa e detalhada.
          </Text>
        </Box>

        <Box
          p={4}
          bg="blue.50"
          borderRadius="md"
          border="1px"
          borderColor="blue.200"
        >
          <Text fontSize="md" fontWeight="medium" color="blue.800" mb={1}>
            Funcionalidades planejadas:
          </Text>
          <VStack align="start" spacing={1} color="blue.700">
            <Text fontSize="sm">• Visualização de entradas e saídas</Text>
            <Text fontSize="sm">• Projeções de fluxo de caixa</Text>
            <Text fontSize="sm">• Relatórios detalhados</Text>
            <Text fontSize="sm">• Análise de tendências</Text>
          </VStack>
        </Box>
      </VStack>
    </Box>
  );
};

export default FluxoCaixaListar;
