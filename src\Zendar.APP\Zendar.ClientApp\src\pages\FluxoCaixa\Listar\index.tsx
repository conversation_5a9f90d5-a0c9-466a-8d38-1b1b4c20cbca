import React, { useState, useEffect } from 'react';
import {
  Box,
  Flex,
  Text,
  Table,
  Thead,
  Tbody,
  Tr,
  Th,
  Td,
  IconButton,
  HStack,
  VStack,
  useColorModeValue,
  Skeleton,
  Badge,
  Tooltip,
  Button,
  Select,
  Input,
  FormControl,
  FormLabel,
  SimpleGrid,
} from '@chakra-ui/react';
import { FiDownload, FiCalendar, FiChevronDown, FiChevronRight } from 'react-icons/fi';

import { FinanceiroMovimentacaoFinanceiraIcon } from 'icons/SemUso';
import { moneyMask } from 'helpers/format/fieldsMasks';
import { FluxoCaixaData, FluxoCaixaItem } from '../types';
import { fluxoCaixaMockData } from '../mockData';

const FluxoCaixaListar: React.FC = () => {
  const [isLoading, setIsLoading] = useState(true);
  const [fluxoCaixaData, setFluxoCaixaData] = useState<FluxoCaixaData | null>(null);
  const [expandedItems, setExpandedItems] = useState<Set<string>>(new Set());
  const [filtros, setFiltros] = useState({
    dataInicio: '2024-01-01',
    dataFim: '2024-09-30',
    periodo: 'jan-set-2024',
  });

  const bgColor = useColorModeValue('white', 'gray.800');
  const borderColor = useColorModeValue('gray.200', 'gray.600');
  const headerBg = useColorModeValue('gray.50', 'gray.700');

  useEffect(() => {
    // Simula carregamento de dados da API
    const timer = setTimeout(() => {
      setFluxoCaixaData(fluxoCaixaMockData);
      setExpandedItems(new Set(['entradas'])); // Expande entradas por padrão
      setIsLoading(false);
    }, 1500);

    return () => clearTimeout(timer);
  }, [filtros]);

  const toggleExpanded = (itemId: string) => {
    const newExpanded = new Set(expandedItems);
    if (newExpanded.has(itemId)) {
      newExpanded.delete(itemId);
    } else {
      newExpanded.add(itemId);
    }
    setExpandedItems(newExpanded);
  };

  const renderItem = (item: FluxoCaixaItem, isChild = false): React.ReactNode => {
    const hasChildren = item.filhos && item.filhos.length > 0;
    const isExpanded = expandedItems.has(item.id);
    const paddingLeft = item.nivel * 20;

    return (
      <React.Fragment key={item.id}>
        <Tr
          bg={isChild ? 'gray.25' : 'white'}
          _hover={{ bg: 'gray.50' }}
          borderBottom="1px"
          borderColor={borderColor}
        >
          <Td
            pl={`${paddingLeft + 16}px`}
            fontWeight={item.tipo === 'categoria' ? 'bold' : item.tipo === 'subcategoria' ? 'semibold' : 'normal'}
            fontSize={item.tipo === 'categoria' ? 'sm' : 'xs'}
            color={item.tipo === 'categoria' ? item.cor : 'gray.700'}
          >
            <Flex align="center">
              {hasChildren && (
                <IconButton
                  aria-label="Expandir/Recolher"
                  icon={isExpanded ? <FiChevronDown /> : <FiChevronRight />}
                  size="xs"
                  variant="ghost"
                  mr={2}
                  onClick={() => toggleExpanded(item.id)}
                />
              )}
              {!hasChildren && <Box w="24px" />}
              <Text>{item.nome}</Text>
            </Flex>
          </Td>
          {Object.entries(item.valores).map(([mes, valor]) => (
            <Td
              key={mes}
              textAlign="right"
              fontSize="xs"
              fontWeight={item.tipo === 'categoria' ? 'bold' : 'normal'}
              color={valor < 0 ? 'red.600' : valor > 0 ? 'green.600' : 'gray.600'}
            >
              {moneyMask(valor, false)}
            </Td>
          ))}
        </Tr>
        {hasChildren && isExpanded && item.filhos?.map(child => renderItem(child, true))}
      </React.Fragment>
    );
  };

  if (isLoading) {
    return (
      <Box p={6}>
        <Flex align="center" mb={6}>
          <FinanceiroMovimentacaoFinanceiraIcon size={24} />
          <Text fontSize="2xl" fontWeight="bold" ml={3}>
            Fluxo de Caixa
          </Text>
        </Flex>
        <VStack spacing={4}>
          {Array.from({ length: 8 }).map((_, index) => (
            <Skeleton key={index} height="40px" width="100%" />
          ))}
        </VStack>
      </Box>
    );
  }

  return (
    <Box p={6}>
      <Flex align="center" justify="space-between" mb={6}>
        <Flex align="center">
          <FinanceiroMovimentacaoFinanceiraIcon size={24} />
          <Text fontSize="2xl" fontWeight="bold" ml={3}>
            Fluxo de Caixa
          </Text>
        </Flex>
        <HStack spacing={2}>
          <Button
            leftIcon={<FiDownload />}
            size="sm"
            variant="outline"
            colorScheme="blue"
          >
            Exportar
          </Button>
          <Badge colorScheme="blue" variant="subtle">
            Janeiro - Setembro 2024
          </Badge>
          <Badge colorScheme="green" variant="subtle">
            Valores em R$
          </Badge>
        </HStack>
      </Flex>

      {/* Filtros */}
      <Box
        bg={bgColor}
        p={4}
        borderRadius="lg"
        border="1px"
        borderColor={borderColor}
        mb={6}
        boxShadow="sm"
      >
        <Text fontSize="md" fontWeight="semibold" mb={3} color="gray.700">
          Filtros
        </Text>
        <SimpleGrid columns={{ base: 1, md: 3, lg: 4 }} spacing={4}>
          <FormControl>
            <FormLabel fontSize="sm">Período</FormLabel>
            <Select
              size="sm"
              value={filtros.periodo}
              onChange={(e) => setFiltros({ ...filtros, periodo: e.target.value })}
            >
              <option value="jan-set-2024">Janeiro - Setembro 2024</option>
              <option value="ano-2024">Ano Completo 2024</option>
              <option value="ultimo-12-meses">Últimos 12 meses</option>
              <option value="personalizado">Período personalizado</option>
            </Select>
          </FormControl>
          <FormControl>
            <FormLabel fontSize="sm">Data Início</FormLabel>
            <Input
              type="date"
              size="sm"
              value={filtros.dataInicio}
              onChange={(e) => setFiltros({ ...filtros, dataInicio: e.target.value })}
            />
          </FormControl>
          <FormControl>
            <FormLabel fontSize="sm">Data Fim</FormLabel>
            <Input
              type="date"
              size="sm"
              value={filtros.dataFim}
              onChange={(e) => setFiltros({ ...filtros, dataFim: e.target.value })}
            />
          </FormControl>
          <FormControl>
            <FormLabel fontSize="sm">Ações</FormLabel>
            <HStack spacing={2}>
              <Button size="sm" colorScheme="blue" variant="solid">
                Aplicar
              </Button>
              <Button size="sm" variant="ghost">
                Limpar
              </Button>
            </HStack>
          </FormControl>
        </SimpleGrid>
      </Box>

      <Box
        bg={bgColor}
        borderRadius="lg"
        border="1px"
        borderColor={borderColor}
        overflow="hidden"
        boxShadow="sm"
      >
        <Box overflowX="auto">
          <Table variant="simple" size="sm">
            <Thead bg={headerBg}>
              <Tr>
                <Th
                  position="sticky"
                  left={0}
                  bg={headerBg}
                  zIndex={1}
                  minW="250px"
                  borderRight="1px"
                  borderColor={borderColor}
                >
                  Conta
                </Th>
                <Th textAlign="right" minW="100px">jan</Th>
                <Th textAlign="right" minW="100px">fev</Th>
                <Th textAlign="right" minW="100px">mar</Th>
                <Th textAlign="right" minW="100px">abr</Th>
                <Th textAlign="right" minW="100px">mai</Th>
                <Th textAlign="right" minW="100px">jun</Th>
                <Th textAlign="right" minW="100px">jul</Th>
                <Th textAlign="right" minW="100px">ago</Th>
                <Th textAlign="right" minW="100px">set</Th>
                <Th textAlign="right" minW="120px" fontWeight="bold">Total</Th>
              </Tr>
            </Thead>
            <Tbody>
              {fluxoCaixaData && (
                <>
                  {renderItem(fluxoCaixaData.saldoInicial)}
                  {renderItem(fluxoCaixaData.entradas)}
                  {renderItem(fluxoCaixaData.saidas)}
                  {renderItem(fluxoCaixaData.transferencias)}
                  {renderItem(fluxoCaixaData.resultadoPeriodo)}
                  {renderItem(fluxoCaixaData.saldoFinal)}
                </>
              )}
            </Tbody>
          </Table>
        </Box>
      </Box>

      {/* Informações da API */}
      <VStack spacing={4} mt={6}>
        <Box p={4} bg="blue.50" borderRadius="md" border="1px" borderColor="blue.200" w="100%">
          <Text fontSize="sm" fontWeight="medium" color="blue.800" mb={3}>
            💡 Estrutura da API para Fluxo de Caixa
          </Text>
          <VStack align="start" spacing={2}>
            <Text fontSize="xs" color="blue.700" fontFamily="mono">
              GET /api/fluxo-caixa?dataInicio=2024-01-01&dataFim=2024-09-30
            </Text>
            <Text fontSize="xs" color="blue.700" fontFamily="mono">
              POST /api/fluxo-caixa/exportar (Excel, PDF, CSV)
            </Text>
            <Text fontSize="xs" color="blue.700">
              • Suporte a filtros por período, conta financeira e plano de contas
            </Text>
            <Text fontSize="xs" color="blue.700">
              • Estrutura hierárquica com categorias, subcategorias e itens
            </Text>
            <Text fontSize="xs" color="blue.700">
              • Totalizadores automáticos e cálculos de variação
            </Text>
          </VStack>
        </Box>

        <Box p={4} bg="green.50" borderRadius="md" border="1px" borderColor="green.200" w="100%">
          <Text fontSize="sm" fontWeight="medium" color="green.800" mb={3}>
            📊 Funcionalidades Implementadas
          </Text>
          <SimpleGrid columns={{ base: 1, md: 2 }} spacing={2}>
            <Text fontSize="xs" color="green.700">✅ Visualização hierárquica de dados</Text>
            <Text fontSize="xs" color="green.700">✅ Expansão/recolhimento de categorias</Text>
            <Text fontSize="xs" color="green.700">✅ Formatação monetária automática</Text>
            <Text fontSize="xs" color="green.700">✅ Filtros por período</Text>
            <Text fontSize="xs" color="green.700">✅ Layout responsivo</Text>
            <Text fontSize="xs" color="green.700">✅ Dados mocados para demonstração</Text>
            <Text fontSize="xs" color="green.700">🔄 Integração com API (em desenvolvimento)</Text>
            <Text fontSize="xs" color="green.700">🔄 Exportação de relatórios (em desenvolvimento)</Text>
          </SimpleGrid>
        </Box>
      </VStack>
    </Box>
  );
};

export default FluxoCaixaListar;
