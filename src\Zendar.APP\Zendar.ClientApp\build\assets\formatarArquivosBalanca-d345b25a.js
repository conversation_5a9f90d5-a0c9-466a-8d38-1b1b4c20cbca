(function(){"use strict";function s(a,t){let o=String(a).replace(/\D/g,"");return o.length>t&&(o="9".repeat(t)),o=o.padStart(t,"0"),o}function l(a){let t=String(a).replace(/\D/g,"");return t.length>3&&(t="999"),t=t.padStart(3,"0"),t}function f(a){let t=a.replace(/[\n\r\t'"]/g," ").replace(/[^\p{L}\s.,-]/gu,"");return t.length>840&&(t=t.substring(0,837)+"..."),t}function g(a,t){let o=a.replace(/[\n\r\t'"]/g," ").replace(/[^\p{L}\s.,-]/gu,"");return o.length>t&&(o=o.substring(0,t)),o}function $(a){var e;const t="01",o="01",r=a.utilizarBalanca?"0":"1",n=String(a.codigoProduto).padStart(6,"0"),i=String(s(Math.round(a.precoVenda*100),6)).padStart(6,"0"),c=String(l(a.diaValidade)).padStart(3,"0"),d=(e=g(a.descricao,25).padEnd(25," "))==null?void 0:e.toLocaleUpperCase();return`${t}${o}${r}${n}${i}${c}${d}`}function u(a){var e;const t="0000000000000000110000000000000000000000000000000000000000000000000000000000000000",o="01",r=a.utilizarBalanca?"0":"1",n=String(a.codigoProduto).padStart(6,"0"),i=String(s(Math.round(a.precoVenda*100),6)).padStart(6,"0"),c=String(l(a.diaValidade)).padStart(3,"0"),d=(e=g(a.descricao,25).padEnd(25," "))==null?void 0:e.toLocaleUpperCase();return`${o}${r}${n}${i}${c}${d}${t}`}function V(a){var S;const t="0000000000110000000000000000000000000000000000000000000000000000000000000000",o="01",r=a.utilizarBalanca?"0":"1",n=String(a.codigoProduto).padStart(6,"0"),i=String(s(Math.round(a.precoVenda*100),6)).padStart(6,"0"),c=String(l(a.diaValidade)).padStart(3,"0"),d=(S=g(a.descricao,50).padEnd(50," "))==null?void 0:S.toLocaleUpperCase(),e=a.possuiComposicao?n:"000000";return`${o}${r}${n}${i}${c}${d}${e}${t}`}function m(a){const t="0".repeat(100),o=f(String(a.composicao));return`${String(a.codigoProduto).padStart(6,"0")}${t}${o}`}function M(a){const t="GERAL".padEnd(12," "),o=String(a).padStart(6,"0"),r="0".repeat(7);return`${t}${o}${r}`}function P(a){var c;const t=String(a.codigoProduto).padStart(6,"0"),o=a.utilizarBalanca?"P":"U",r=(c=g(a.descricao,22).padEnd(22," "))==null?void 0:c.toLocaleUpperCase(),n=String(s(Math.round(a.precoVenda*100),7)).padStart(7,"0"),i=String(l(a.diaValidade)).padStart(3,"0");return`${t}${o}${r}${n}${i}`}const p={toledo:(a,t)=>{const o={MGV:$,MGV5:u,MGV6:V,INFO_MGV6:m};return a==null?void 0:a.map(r=>o[t](r))},filizola:(a,t)=>{const o={SETORES:M,CADTXT:P};return t==="SETORES"?a==null?void 0:a.map(r=>o[t](r.codigoProduto)):a==null?void 0:a.map(r=>o[t](r))}};onmessage=a=>{try{let t;a.data.balanca==="TOLEDO"?t=p.toledo(a.data.produtos,a.data.formato):t=p.filizola(a.data.produtos,a.data.formato),postMessage({status:"success",data:t,nomeArquivo:a.data.nomeArquivo})}catch{postMessage({status:"error",error:"Não foi possível exportar dados"})}}})();
